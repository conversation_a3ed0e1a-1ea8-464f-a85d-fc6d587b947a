{"Description": "Complex CloudFront distribution with multiple origins and custom domain", "Resources": {"PublisherPortalCache8BEB5AAB": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Cache policy for publisher portal", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "PublisherPortalCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "all"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "all"}}}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/PublisherPortalCache/Resource"}}, "SftpHandlerCacheD292D7B8": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Cache policy for SFTP handler", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "SftpHandlerCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "all"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "all"}}}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/SftpHandlerCache/Resource"}}, "BulkEditCacheFC5EA5C2": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Cache policy for bulk edit", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "BulkEditCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "all"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "all"}}}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/BulkEditCache/Resource"}}, "KubeCacheAC2B3D63": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Cache policy for kube endpoints", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "KubeCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "all"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "whitelist", "Headers": ["Authorization", "Origin", "Accept-Encoding"]}, "QueryStringsConfig": {"QueryStringBehavior": "all"}}}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/KubeCache/Resource"}}, "ComplexDistributionOrigin2S3Origin5D9E4DD2": {"Type": "AWS::CloudFront::CloudFrontOriginAccessIdentity", "Properties": {"CloudFrontOriginAccessIdentityConfig": {"Comment": "Identity for ComplexDistributionStackComplexDistributionOrigin22C9F7CB1"}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/ComplexDistribution/Origin2/S3Origin/Resource"}}, "ComplexDistribution89A8BF2B": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": ["test-publisher.delty.com"], "CacheBehaviors": [{"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "SftpHandlerCacheD292D7B8"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "PathPattern": "/sftphandler/*", "TargetOriginId": "ComplexDistributionStackComplexDistributionOrigin22C9F7CB1", "ViewerProtocolPolicy": "redirect-to-https"}, {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "BulkEditCacheFC5EA5C2"}, "CachedMethods": ["GET", "HEAD"], "Compress": true, "PathPattern": "/bulkedit/*", "TargetOriginId": "ComplexDistributionStackComplexDistributionOrigin3CA4983F9", "ViewerProtocolPolicy": "redirect-to-https"}, {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "KubeCacheAC2B3D63"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "PathPattern": "kube*", "TargetOriginId": "ComplexDistributionStackComplexDistributionOrigin44AF468E7", "ViewerProtocolPolicy": "redirect-to-https"}], "Comment": "Complex Multi-Origin Distribution - ES27SJ7X3UX1M Recreation", "CustomErrorResponses": [{"ErrorCachingMinTTL": 0, "ErrorCode": 403}, {"ErrorCachingMinTTL": 0, "ErrorCode": 404}, {"ErrorCachingMinTTL": 0, "ErrorCode": 500}, {"ErrorCachingMinTTL": 0, "ErrorCode": 501}, {"ErrorCachingMinTTL": 0, "ErrorCode": 502}, {"ErrorCachingMinTTL": 0, "ErrorCode": 503}, {"ErrorCachingMinTTL": 0, "ErrorCode": 504}], "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "PublisherPortalCache8BEB5AAB"}, "CachedMethods": ["GET", "HEAD"], "Compress": true, "TargetOriginId": "ComplexDistributionStackComplexDistributionOrigin16CC7FE64", "ViewerProtocolPolicy": "redirect-to-https"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 60, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "dqhg2xtwi2.execute-api.us-east-1.amazonaws.com", "Id": "ComplexDistributionStackComplexDistributionOrigin16CC7FE64", "OriginPath": "/dev/portal"}, {"DomainName": "deleteonly-dev.s3.us-east-1.amazonaws.com", "Id": "ComplexDistributionStackComplexDistributionOrigin22C9F7CB1", "S3OriginConfig": {"OriginAccessIdentity": {"Fn::Join": ["", ["origin-access-identity/cloudfront/", {"Ref": "ComplexDistributionOrigin2S3Origin5D9E4DD2"}]]}}}, {"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "s3.amazonaws.com", "Id": "ComplexDistributionStackComplexDistributionOrigin3CA4983F9", "OriginPath": "/deleteonly-dev"}, {"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 60, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 60, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "dqhg2xtwi2.execute-api.us-east-1.amazonaws.com", "Id": "ComplexDistributionStackComplexDistributionOrigin44AF468E7", "OriginPath": "/dev"}], "PriceClass": "PriceClass_All", "ViewerCertificate": {"AcmCertificateArn": "arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196", "MinimumProtocolVersion": "TLSv1.2_2021", "SslSupportMethod": "sni-only"}}}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/ComplexDistribution/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/+1VwW4aMRD9lvhYmS0hUg/cAlWlHNogqHpZocjYQ5jg9WzHYyha8e+VyW6AKDlWSaSeVm9mdvTmvbE9KAb9fnF5YbaxZ92653FRNDMxdq2nECmxhXKuzTbeNdZTckumIEUzNnYFE/Jod2Wj7BH9MBWoofqktLJUVRCkRQ6WJnn5Kb4NVObPCcBwBCswDngEK7NB4jb4OwHvZsIY7p9lLNEa4Sy41/9JfWRScz1ehpMd018xCuMiCVLIC3fGDYJZeHBqKJxAq5VI/Qs4IgU1PKCB0qpmtDD2JkY1VJMncHft/VOLm3rzpetigQWXaI10gjiqDIasUDwX5ci9UcR4j6HNbxC2wBMmIUv+cRQ1VAwOGaz0hHqZXlRaGe9pC+47yIpc1/9gzPMYVTVDnqLleVSpNVQZ5zALZXzHrPsZmImnEGsKMU9RNgd9ZmIkdTXSGpk34wMl5zltnDuaUeaCJof/rQ9L4+NLRrwdo1c24z1KNNe3hxNzbS3EeOMgCMqubNorID853/KT81JVrji9GPY6XhWjZNcgIxOhnO9zxW2SOsleB3JQPMTPm8GguLwq+hcPEbHHKQhWUEwfv38BSW21EA8HAAA="}, "Metadata": {"aws:cdk:path": "ComplexDistributionStack/CDKMetadata/Default"}}}, "Outputs": {"ComplexDistributionDomainName": {"Description": "CloudFront domain name for complex distribution", "Value": {"Fn::GetAtt": ["ComplexDistribution89A8BF2B", "DomainName"]}}, "ComplexDistributionId": {"Description": "CloudFront distribution ID for complex distribution", "Value": {"Ref": "ComplexDistribution89A8BF2B"}}, "CustomDomainName": {"Description": "Custom domain name for the distribution", "Value": "test-publisher.delty.com"}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}