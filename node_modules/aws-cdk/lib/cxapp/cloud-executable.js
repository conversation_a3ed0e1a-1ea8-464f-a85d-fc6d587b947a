"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudExecutable = void 0;
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const cloud_assembly_1 = require("./cloud-assembly");
const api_private_1 = require("../../lib/api-private");
const singleton_plugin_host_1 = require("../cli/singleton-plugin-host");
const contextproviders = require("../context-providers");
/**
 * Represent the Cloud Executable and the synthesis we can do on it
 */
class CloudExecutable {
    constructor(props) {
        this.props = props;
    }
    async produce() {
        const synthesisResult = await this.synthesize(true);
        // We must return an `IReadableCloudAssembly` here, but this Cloud Assembly is only used in the context
        // of the CLI and `cli.ts` currently manages its own locking in the "synthesizer" callback function.
        //
        // All the lock-related functions are therefore no-ops.
        return new api_private_1.BorrowedAssembly(synthesisResult.assembly);
    }
    /**
     * Return whether there is an app command from the configuration
     */
    get hasApp() {
        return !!this.props.configuration.settings.get(['app']);
    }
    /**
     * Synthesize a set of stacks.
     *
     * @param cacheCloudAssembly - whether to cache the Cloud Assembly after it has been first synthesized.
     *   This is 'true' by default, and only set to 'false' for 'cdk watch',
     *   which needs to re-synthesize the Assembly each time it detects a change to the project files
     */
    async synthesize(cacheCloudAssembly = true) {
        if (!this._cloudAssembly || !cacheCloudAssembly) {
            this._cloudAssembly = await this.doSynthesize();
        }
        return this._cloudAssembly;
    }
    async doSynthesize() {
        // We may need to run the cloud executable multiple times in order to satisfy all missing context
        // (When the executable runs, it will tell us about context it wants to use
        // but it missing. We'll then look up the context and run the executable again, and
        // again, until it doesn't complain anymore or we've stopped making progress).
        let previouslyMissingKeys;
        while (true) {
            const assembly = await this.props.synthesizer(this.props.sdkProvider, this.props.configuration);
            if (assembly.manifest.missing && assembly.manifest.missing.length > 0) {
                const missingKeys = missingContextKeys(assembly.manifest.missing);
                if (!this.canLookup) {
                    throw new toolkit_lib_1.ToolkitError('Context lookups have been disabled. '
                        + 'Make sure all necessary context is already in \'cdk.context.json\' by running \'cdk synth\' on a machine with sufficient AWS credentials and committing the result. '
                        + `Missing context keys: '${Array.from(missingKeys).join(', ')}'`);
                }
                let tryLookup = true;
                if (previouslyMissingKeys && setsEqual(missingKeys, previouslyMissingKeys)) {
                    await this.props.ioHelper.defaults.debug('Not making progress trying to resolve environmental context. Giving up.');
                    tryLookup = false;
                }
                previouslyMissingKeys = missingKeys;
                if (tryLookup) {
                    await this.props.ioHelper.defaults.debug('Some context information is missing. Fetching...');
                    const updates = await contextproviders.provideContextValues(assembly.manifest.missing, this.props.sdkProvider, singleton_plugin_host_1.GLOBAL_PLUGIN_HOST, this.props.ioHelper);
                    for (const [key, value] of Object.entries(updates)) {
                        this.props.configuration.context.set(key, value);
                    }
                    // Cache the new context to disk
                    await this.props.configuration.saveContext();
                    // Execute again
                    continue;
                }
            }
            return new cloud_assembly_1.CloudAssembly(assembly, this.props.ioHelper);
        }
    }
    get canLookup() {
        return !!(this.props.configuration.settings.get(['lookups']) ?? true);
    }
}
exports.CloudExecutable = CloudExecutable;
/**
 * Return all keys of missing context items
 */
function missingContextKeys(missing) {
    return new Set((missing || []).map(m => m.key));
}
function setsEqual(a, b) {
    if (a.size !== b.size) {
        return false;
    }
    for (const x of a) {
        if (!b.has(x)) {
            return false;
        }
    }
    return true;
}
//# sourceMappingURL=data:application/json;base64,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