#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const cdk = __importStar(require("aws-cdk-lib"));
const awsmcpdemo_stack_1 = require("../lib/awsmcpdemo-stack");
const api_gateway_distribution_stack_1 = require("../lib/api-gateway-distribution-stack");
const s3_website_distribution_stack_1 = require("../lib/s3-website-distribution-stack");
const complex_distribution_stack_1 = require("../lib/complex-distribution-stack");
const app = new cdk.App();
// Environment configuration for your AWS account
const env = {
    account: '************',
    region: 'us-east-1',
};
// Main placeholder stack
new awsmcpdemo_stack_1.AwsmcpdemoStack(app, 'AwsmcpdemoStack', {
    env: env,
    description: 'Main placeholder stack for AWS MCP Demo',
});
// API Gateway CloudFront distributions
new api_gateway_distribution_stack_1.ApiGatewayDistributionStack(app, 'ApiGatewayDistributionStack', {
    env: env,
    description: 'CloudFront distributions for API Gateway origins',
});
// S3 Website and external website distributions
new s3_website_distribution_stack_1.S3WebsiteDistributionStack(app, 'S3WebsiteDistributionStack', {
    env: env,
    description: 'CloudFront distributions for S3 websites and external origins',
});
// Complex multi-origin distribution with custom domain
new complex_distribution_stack_1.ComplexDistributionStack(app, 'ComplexDistributionStack', {
    env: env,
    description: 'Complex CloudFront distribution with multiple origins and custom domain',
});
//# sourceMappingURL=data:application/json;base64,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