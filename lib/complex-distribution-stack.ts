import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as certificatemanager from 'aws-cdk-lib/aws-certificatemanager';
import * as s3 from 'aws-cdk-lib/aws-s3';

export class ComplexDistributionStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Import the existing ACM certificate
    const certificate = certificatemanager.Certificate.fromCertificateArn(
      this,
      'DeltyTestCertificate',
      'arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196'
    );

    // Distribution ES27SJ7X3UX1M - Complex multi-origin distribution with custom domain
    const complexDistribution = new cloudfront.Distribution(this, 'ComplexDistribution', {
      comment: 'Complex Multi-Origin Distribution - ES27SJ7X3UX1M Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      certificate: certificate,
      domainNames: ['test-publisher.delty.com'],
      
      // Default behavior - Publisher Portal
      defaultBehavior: {
        origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
          originPath: '/dev/portal',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(60),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: true,
        cachePolicy: new cloudfront.CachePolicy(this, 'PublisherPortalCache', {
          cachePolicyName: 'PublisherPortalCachePolicy',
          comment: 'Cache policy for publisher portal',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
          cookieBehavior: cloudfront.CacheCookieBehavior.all(),
        }),
      },
      
      additionalBehaviors: {
        // SFTP Handler behavior - S3 origin
        '/sftphandler/*': {
          origin: new origins.S3Origin(
            // Note: This references an existing S3 bucket
            // You may need to import or create this bucket separately
            s3.Bucket.fromBucketName(this, 'DeleteOnlyDevBucket', 'deleteonly-dev')
          ),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'SftpHandlerCache', {
            cachePolicyName: 'SftpHandlerCachePolicy',
            comment: 'Cache policy for SFTP handler',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },
        
        // Bulk Edit behavior - S3 custom origin
        '/bulkedit/*': {
          origin: new origins.HttpOrigin('s3.amazonaws.com', {
            originPath: '/deleteonly-dev',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
            httpsPort: 443,
            httpPort: 80,
            readTimeout: cdk.Duration.seconds(30),
            keepaliveTimeout: cdk.Duration.seconds(5),
            connectionAttempts: 3,
            connectionTimeout: cdk.Duration.seconds(10),
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,
          cachePolicy: new cloudfront.CachePolicy(this, 'BulkEditCache', {
            cachePolicyName: 'BulkEditCachePolicy',
            comment: 'Cache policy for bulk edit',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },
        
        // Kube behavior - Publisher Kube API
        'kube*': {
          origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
            originPath: '/dev',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
            httpsPort: 443,
            httpPort: 80,
            readTimeout: cdk.Duration.seconds(60),
            keepaliveTimeout: cdk.Duration.seconds(60),
            connectionAttempts: 3,
            connectionTimeout: cdk.Duration.seconds(10),
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'KubeCache', {
            cachePolicyName: 'KubeCachePolicy',
            comment: 'Cache policy for kube endpoints',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
              'Authorization',
              'Origin',
              'Accept-Encoding'
            ),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },
      },
      
      // Custom error responses
      errorResponses: [
        {
          httpStatus: 403,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 404,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 500,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 501,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 502,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 503,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 504,
          ttl: cdk.Duration.seconds(0),
        },
      ],
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Output the distribution information
    new cdk.CfnOutput(this, 'ComplexDistributionDomainName', {
      value: complexDistribution.distributionDomainName,
      description: 'CloudFront domain name for complex distribution',
    });

    new cdk.CfnOutput(this, 'ComplexDistributionId', {
      value: complexDistribution.distributionId,
      description: 'CloudFront distribution ID for complex distribution',
    });

    new cdk.CfnOutput(this, 'CustomDomainName', {
      value: 'test-publisher.delty.com',
      description: 'Custom domain name for the distribution',
    });
  }
}
