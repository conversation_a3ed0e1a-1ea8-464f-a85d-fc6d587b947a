# CloudFront CDK Configuration Recreation

This project recreates your existing AWS CloudFront distributions using AWS CDK (Cloud Development Kit) with TypeScript.

## Overview

Based on your existing CloudFront distributions, I've created CDK stacks that recreate the following distributions:

### 1. API Gateway Distribution Stack (`ApiGatewayDistributionStack`)
Recreates distributions that use API Gateway as origins:
- **E1Y2GZXHXCGI8H**: API Gateway with custom headers forwarding
- **E131RZ69QYLX7O**: API Gateway with cache behaviors for JPG files
- **E2EZONTUD8DA1O**: API Gateway with device detection headers

### 2. S3 Website Distribution Stack (`S3WebsiteDistributionStack`)
Recreates distributions for S3 websites and external origins:
- **E1YNWVHTAGY30T**: S3 website with WAF protection
- **EXZC66UKJAABK**: External website (quotewizard.com)
- **E10NZFW9D6AMU2**: Multi-origin distribution

### 3. Complex Distribution Stack (`ComplexDistributionStack`)
Recreates the most complex distribution:
- **ES27SJ7X3UX1M**: Multi-origin distribution with custom domain (test-publisher.delty.com)
  - Multiple origins (API Gateway, S3)
  - Custom cache behaviors for different path patterns
  - SSL certificate integration
  - Custom error responses

## Project Structure

```
├── bin/
│   └── awsmcpdemo.ts                    # Main CDK app entry point
├── lib/
│   ├── awsmcpdemo-stack.ts             # Main placeholder stack
│   ├── api-gateway-distribution-stack.ts # API Gateway distributions
│   ├── s3-website-distribution-stack.ts  # S3 and website distributions
│   └── complex-distribution-stack.ts     # Complex multi-origin distribution
├── package.json                         # Dependencies and scripts
└── CLOUDFRONT_CDK_README.md            # This file
```

## Prerequisites

1. **AWS CLI configured** with your profile:
   ```bash
   aws configure sso --profile AWS-dev-Delty-Admin-902537659456
   ```

2. **Node.js and npm** installed

3. **AWS CDK CLI** installed:
   ```bash
   npm install -g aws-cdk
   ```

## Setup and Deployment

### 1. Install Dependencies
```bash
npm install
```

### 2. Build the Project
```bash
npm run build
```

### 3. Bootstrap CDK (if not done before)
```bash
npx cdk bootstrap --profile AWS-dev-Delty-Admin-902537659456
```

### 4. Synthesize CloudFormation Templates
```bash
npx cdk synth --profile AWS-dev-Delty-Admin-902537659456
```

### 5. Deploy Individual Stacks

Deploy API Gateway distributions:
```bash
npx cdk deploy ApiGatewayDistributionStack --profile AWS-dev-Delty-Admin-902537659456
```

Deploy S3 website distributions:
```bash
npx cdk deploy S3WebsiteDistributionStack --profile AWS-dev-Delty-Admin-902537659456
```

Deploy complex distribution:
```bash
npx cdk deploy ComplexDistributionStack --profile AWS-dev-Delty-Admin-902537659456
```

Deploy all stacks:
```bash
npx cdk deploy --all --profile AWS-dev-Delty-Admin-902537659456
```

## Important Notes

### 1. Existing Resources
- The CDK stacks reference existing resources (S3 buckets, certificates)
- Make sure these resources exist before deployment:
  - S3 bucket: `deleteonly-dev`
  - ACM certificate: `arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196`

### 2. Custom Domain
- The complex distribution uses `test-publisher.delty.com`
- Ensure DNS is properly configured to point to the CloudFront distribution

### 3. WAF Web ACL
- The S3 website distribution includes a WAF Web ACL
- This will create a new WAF resource (the existing one is referenced by ARN)

### 4. Cache Policies
- Custom cache policies are created for each distribution
- These replicate the behavior of your existing distributions

## Verification

After deployment, verify the distributions match your existing ones:

1. **Check distribution settings** in AWS Console
2. **Test origins** and cache behaviors
3. **Verify custom domains** resolve correctly
4. **Test error responses** and WAF rules

## Customization

### Modifying Distributions
- Edit the respective stack files in `lib/`
- Update cache policies, origins, or behaviors as needed
- Run `npm run build` and `npx cdk deploy` to apply changes

### Adding New Distributions
- Create new stack files following the existing patterns
- Import and instantiate in `bin/awsmcpdemo.ts`

## Useful Commands

- `npm run build`   - Compile TypeScript to JavaScript
- `npm run watch`   - Watch for changes and compile
- `npm run test`    - Run Jest unit tests
- `npx cdk deploy`  - Deploy stacks to AWS
- `npx cdk diff`    - Compare deployed stack with current state
- `npx cdk synth`   - Emit synthesized CloudFormation template
- `npx cdk destroy` - Delete stacks from AWS

## Troubleshooting

### Common Issues
1. **Certificate not found**: Ensure the ACM certificate exists in us-east-1
2. **S3 bucket not found**: Create the `deleteonly-dev` bucket or update the reference
3. **Permission errors**: Ensure your AWS profile has CloudFront and related permissions

### Getting Help
- Check AWS CDK documentation: https://docs.aws.amazon.com/cdk/
- Review CloudFront CDK constructs: https://docs.aws.amazon.com/cdk/api/v2/docs/aws-cdk-lib.aws-cloudfront-readme.html
