"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.YARGS_HELPERS = void 0;
exports.makeConfig = makeConfig;
const cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
// eslint-disable-next-line import/no-extraneous-dependencies
const user_input_gen_1 = require("@aws-cdk/user-input-gen");
const cdk_from_cfn = require("cdk-from-cfn");
const deploy_1 = require("../commands/deploy");
const init_1 = require("../commands/init");
exports.YARGS_HELPERS = new user_input_gen_1.CliHelpers('./util/yargs-helpers');
/**
 * Source of truth for all CDK CLI commands. `user-input-gen` translates this into:
 *
 * - the `yargs` definition in `lib/parse-command-line-arguments.ts`.
 * - the `UserInput` type in `lib/user-input.ts`.
 * - the `convertXxxToUserInput` functions in `lib/convert-to-user-input.ts`.
 */
async function makeConfig() {
    return {
        globalOptions: {
            'app': { type: 'string', alias: 'a', desc: 'REQUIRED WHEN RUNNING APP: command-line for executing your app or a cloud assembly directory (e.g. "node bin/my-app.js"). Can also be specified in cdk.json or ~/.cdk.json', requiresArg: true },
            'build': { type: 'string', desc: 'Command-line for a pre-synth build' },
            'context': { type: 'array', alias: 'c', desc: 'Add contextual string parameter (KEY=VALUE)' },
            'plugin': { type: 'array', alias: 'p', desc: 'Name or path of a node package that extend the CDK features. Can be specified multiple times' },
            'trace': { type: 'boolean', desc: 'Print trace for stack warnings' },
            'strict': { type: 'boolean', desc: 'Do not construct stacks with warnings' },
            'lookups': { type: 'boolean', desc: 'Perform context lookups (synthesis fails if this is disabled and context lookups need to be performed)', default: true },
            'ignore-errors': { type: 'boolean', default: false, desc: 'Ignores synthesis errors, which will likely produce an invalid output' },
            'json': { type: 'boolean', alias: 'j', desc: 'Use JSON output instead of YAML when templates are printed to STDOUT', default: false },
            'verbose': { type: 'boolean', alias: 'v', desc: 'Show debug logs (specify multiple times to increase verbosity)', default: false, count: true },
            'debug': { type: 'boolean', desc: 'Debug the CDK app. Log additional information during synthesis, such as creation stack traces of tokens (sets CDK_DEBUG, will slow down synthesis)', default: false },
            'profile': { type: 'string', desc: 'Use the indicated AWS profile as the default environment', requiresArg: true },
            'proxy': { type: 'string', desc: 'Use the indicated proxy. Will read from HTTPS_PROXY environment variable if not specified', requiresArg: true },
            'ca-bundle-path': { type: 'string', desc: 'Path to CA certificate to use when validating HTTPS requests. Will read from AWS_CA_BUNDLE environment variable if not specified', requiresArg: true },
            'ec2creds': { type: 'boolean', alias: 'i', default: undefined, desc: 'Force trying to fetch EC2 instance credentials. Default: guess EC2 instance status' },
            'version-reporting': { type: 'boolean', desc: 'Include the "AWS::CDK::Metadata" resource in synthesized templates (enabled by default)', default: undefined },
            'path-metadata': { type: 'boolean', desc: 'Include "aws:cdk:path" CloudFormation metadata for each resource (enabled by default)', default: undefined },
            'asset-metadata': { type: 'boolean', desc: 'Include "aws:asset:*" CloudFormation metadata for resources that uses assets (enabled by default)', default: undefined },
            'role-arn': { type: 'string', alias: 'r', desc: 'ARN of Role to use when invoking CloudFormation', default: undefined, requiresArg: true },
            'staging': { type: 'boolean', desc: 'Copy assets to the output directory (use --no-staging to disable the copy of assets which allows local debugging via the SAM CLI to reference the original source files)', default: true },
            'output': { type: 'string', alias: 'o', desc: 'Emits the synthesized cloud assembly into a directory (default: cdk.out)', requiresArg: true },
            'notices': { type: 'boolean', desc: 'Show relevant notices', default: exports.YARGS_HELPERS.shouldDisplayNotices() },
            'no-color': { type: 'boolean', desc: 'Removes colors and other style from console output', default: false },
            'ci': { type: 'boolean', desc: 'Force CI detection. If CI=true then logs will be sent to stdout instead of stderr', default: exports.YARGS_HELPERS.isCI() },
            'unstable': { type: 'array', desc: 'Opt in to unstable features. The flag indicates that the scope and API of a feature might still change. Otherwise the feature is generally production ready and fully supported. Can be specified multiple times.', default: [] },
        },
        commands: {
            list: {
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                aliases: ['ls'],
                description: 'Lists all stacks in the app',
                options: {
                    'long': { type: 'boolean', default: false, alias: 'l', desc: 'Display environment information for each stack' },
                    'show-dependencies': { type: 'boolean', default: false, alias: 'd', desc: 'Display stack dependency information for each stack' },
                },
            },
            synth: {
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                aliases: ['synthesize'],
                description: 'Synthesizes and prints the CloudFormation template for this stack',
                options: {
                    exclusively: { type: 'boolean', alias: 'e', desc: 'Only synthesize requested stacks, don\'t include dependencies' },
                    validation: { type: 'boolean', desc: 'After synthesis, validate stacks with the "validateOnSynth" attribute set (can also be controlled with CDK_VALIDATION)', default: true },
                    quiet: { type: 'boolean', alias: 'q', desc: 'Do not output CloudFormation Template to stdout', default: false },
                },
            },
            bootstrap: {
                arg: {
                    name: 'ENVIRONMENTS',
                    variadic: true,
                },
                description: 'Deploys the CDK toolkit stack into an AWS environment',
                options: {
                    'bootstrap-bucket-name': { type: 'string', alias: ['b', 'toolkit-bucket-name'], desc: 'The name of the CDK toolkit bucket; bucket will be created and must not exist', default: undefined },
                    'bootstrap-kms-key-id': { type: 'string', desc: 'AWS KMS master key ID used for the SSE-KMS encryption (specify AWS_MANAGED_KEY to use an AWS-managed key)', default: undefined, conflicts: 'bootstrap-customer-key' },
                    'example-permissions-boundary': { type: 'boolean', alias: 'epb', desc: 'Use the example permissions boundary.', default: undefined, conflicts: 'custom-permissions-boundary' },
                    'custom-permissions-boundary': { type: 'string', alias: 'cpb', desc: 'Use the permissions boundary specified by name.', default: undefined, conflicts: 'example-permissions-boundary' },
                    'bootstrap-customer-key': { type: 'boolean', desc: 'Create a Customer Master Key (CMK) for the bootstrap bucket (you will be charged but can customize permissions, modern bootstrapping only)', default: undefined, conflicts: 'bootstrap-kms-key-id' },
                    'qualifier': { type: 'string', desc: 'String which must be unique for each bootstrap stack. You must configure it on your CDK app if you change this from the default.', default: undefined },
                    'public-access-block-configuration': { type: 'boolean', desc: 'Block public access configuration on CDK toolkit bucket (enabled by default) ', default: undefined },
                    'tags': { type: 'array', alias: 't', desc: 'Tags to add for the stack (KEY=VALUE)', default: [] },
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)', default: true },
                    'trust': { type: 'array', desc: 'The AWS account IDs that should be trusted to perform deployments into this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'trust-for-lookup': { type: 'array', desc: 'The AWS account IDs that should be trusted to look up values in this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'untrust': { type: 'array', desc: 'The AWS account IDs that should not be trusted by this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'cloudformation-execution-policies': { type: 'array', desc: 'The Managed Policy ARNs that should be attached to the role performing deployments into this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always bootstrap even if it would downgrade template version', default: false },
                    'termination-protection': { type: 'boolean', default: undefined, desc: 'Toggle CloudFormation termination protection on the bootstrap stacks' },
                    'show-template': { type: 'boolean', desc: 'Instead of actual bootstrapping, print the current CLI\'s bootstrapping template to stdout for customization', default: false },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack to create', requiresArg: true },
                    'template': { type: 'string', requiresArg: true, desc: 'Use the template from the given file instead of the built-in one (use --show-template to obtain an example)' },
                    'previous-parameters': { type: 'boolean', default: true, desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)' },
                },
            },
            gc: {
                description: 'Garbage collect assets. Options detailed here: https://github.com/aws/aws-cdk-cli/tree/main/packages/aws-cdk#cdk-gc',
                arg: {
                    name: 'ENVIRONMENTS',
                    variadic: true,
                },
                options: {
                    'action': { type: 'string', desc: 'The action (or sub-action) you want to perform. Valid entires are "print", "tag", "delete-tagged", "full".', default: 'full' },
                    'type': { type: 'string', desc: 'Specify either ecr, s3, or all', default: 'all' },
                    'rollback-buffer-days': { type: 'number', desc: 'Delete assets that have been marked as isolated for this many days', default: 0 },
                    'created-buffer-days': { type: 'number', desc: 'Never delete assets younger than this (in days)', default: 1 },
                    'confirm': { type: 'boolean', desc: 'Confirm via manual prompt before deletion', default: true },
                    'bootstrap-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack, if different from the default "CDKToolkit"', requiresArg: true },
                },
            },
            deploy: {
                description: 'Deploys the stack(s) named STACKS into your AWS account',
                options: {
                    'all': { type: 'boolean', desc: 'Deploy all available stacks', default: false },
                    'build-exclude': { type: 'array', alias: 'E', desc: 'Do not rebuild asset with the given ID. Can be specified multiple times', default: [] },
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only deploy requested stacks, don\'t include dependencies' },
                    'require-approval': { type: 'string', choices: [cloud_assembly_schema_1.RequireApproval.NEVER, cloud_assembly_schema_1.RequireApproval.ANYCHANGE, cloud_assembly_schema_1.RequireApproval.BROADENING], desc: 'What security-sensitive changes need manual approval' },
                    'notification-arns': { type: 'array', desc: 'ARNs of SNS topics that CloudFormation will notify with stack related events. These will be added to ARNs specified with the \'notificationArns\' stack property.' },
                    // @deprecated(v2) -- tags are part of the Cloud Assembly and tags specified here will be overwritten on the next deployment
                    'tags': { type: 'array', alias: 't', desc: 'Tags to add to the stack (KEY=VALUE), overrides tags from Cloud Assembly (deprecated)' },
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet) (deprecated)', deprecated: true },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create (only if method is not direct)' },
                    'method': {
                        alias: 'm',
                        type: 'string',
                        choices: ['direct', 'change-set', 'prepare-change-set'],
                        requiresArg: true,
                        desc: 'How to perform the deployment. Direct is a bit faster but lacks progress information',
                    },
                    'import-existing-resources': { type: 'boolean', desc: 'Indicates if the stack set imports resources that already exist.', default: false },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always deploy stack even if templates are identical', default: false },
                    'parameters': { type: 'array', desc: 'Additional parameters passed to CloudFormation at deploy time (STACK:KEY=VALUE)', default: {} },
                    'outputs-file': { type: 'string', alias: 'O', desc: 'Path to file where stack outputs will be written as JSON', requiresArg: true },
                    'previous-parameters': { type: 'boolean', default: true, desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)', requiresArg: true },
                    'progress': { type: 'string', choices: [deploy_1.StackActivityProgress.BAR, deploy_1.StackActivityProgress.EVENTS], desc: 'Display mode for stack activity events' },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                        negativeAlias: 'R',
                    },
                    'hotswap': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'but does not fall back to a full deployment if that is not possible. ' +
                            'Instead, changes to any non-hotswappable properties are ignored.' +
                            'Do not use this in production environments',
                    },
                    'hotswap-fallback': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'which skips CloudFormation and updates the resources directly, ' +
                            'and falls back to a full deployment if that is not possible. ' +
                            'Do not use this in production environments',
                    },
                    'hotswap-ecs-minimum-healthy-percent': {
                        type: 'string',
                        desc: 'Lower limit on the number of your service\'s tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-maximum-healthy-percent': {
                        type: 'string',
                        desc: 'Upper limit on the number of your service\'s tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-stabilization-timeout-seconds': {
                        type: 'string',
                        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
                    },
                    'watch': {
                        type: 'boolean',
                        desc: 'Continuously observe the project files, ' +
                            'and deploy the given stack(s) automatically when changes are detected. ' +
                            'Implies --hotswap by default',
                    },
                    'logs': {
                        type: 'boolean',
                        default: true,
                        desc: 'Show CloudWatch log events from all resources in the selected Stacks in the terminal. ' +
                            "'true' by default, use --no-logs to turn off. " +
                            "Only in effect if specified alongside the '--watch' option",
                    },
                    'concurrency': { type: 'number', desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.', default: 1, requiresArg: true },
                    'asset-parallelism': { type: 'boolean', desc: 'Whether to build/publish assets in parallel' },
                    'asset-prebuild': { type: 'boolean', desc: 'Whether to build all assets before deploying the first stack (useful for failing Docker builds)', default: true },
                    'ignore-no-stacks': { type: 'boolean', desc: 'Whether to deploy if the app contains no stacks', default: false },
                },
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
            },
            rollback: {
                description: 'Rolls back the stack(s) named STACKS to their last stable state',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'all': { type: 'boolean', default: false, desc: 'Roll back all available stacks' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack the environment is bootstrapped with', requiresArg: true },
                    'force': {
                        alias: 'f',
                        type: 'boolean',
                        desc: 'Orphan all resources for which the rollback operation fails.',
                    },
                    'validate-bootstrap-version': {
                        type: 'boolean',
                        desc: 'Whether to validate the bootstrap stack version. Defaults to \'true\', disable with --no-validate-bootstrap-version.',
                    },
                    'orphan': {
                        // alias: 'o' conflicts with --output
                        type: 'array',
                        desc: 'Orphan the given resources, identified by their logical ID (can be specified multiple times)',
                        default: [],
                    },
                },
            },
            import: {
                description: 'Import existing resource(s) into the given STACK',
                arg: {
                    name: 'STACK',
                    variadic: false,
                },
                options: {
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)', default: true },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack to create', requiresArg: true },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                    },
                    'force': {
                        alias: 'f',
                        type: 'boolean',
                        desc: 'Do not abort if the template diff includes updates or deletes. This is probably safe but we\'re not sure, let us know how it goes.',
                    },
                    'record-resource-mapping': {
                        type: 'string',
                        alias: 'r',
                        requiresArg: true,
                        desc: 'If specified, CDK will generate a mapping of existing physical resources to CDK resources to be imported as. The mapping ' +
                            'will be written in the given file path. No actual import operation will be performed',
                    },
                    'resource-mapping': {
                        type: 'string',
                        alias: 'm',
                        requiresArg: true,
                        desc: 'If specified, CDK will use the given file to map physical resources to CDK resources for import, instead of interactively ' +
                            'asking the user. Can be run from scripts',
                    },
                },
            },
            watch: {
                description: "Shortcut for 'deploy --watch'",
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'build-exclude': { type: 'array', alias: 'E', desc: 'Do not rebuild asset with the given ID. Can be specified multiple times', default: [] },
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only deploy requested stacks, don\'t include dependencies' },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create' },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always deploy stack even if templates are identical', default: false },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)', requiresArg: true },
                    'progress': { type: 'string', choices: [deploy_1.StackActivityProgress.BAR, deploy_1.StackActivityProgress.EVENTS], desc: 'Display mode for stack activity events' },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                        negativeAlias: 'R',
                    },
                    'hotswap': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'but does not fall back to a full deployment if that is not possible. ' +
                            'Instead, changes to any non-hotswappable properties are ignored.' +
                            "'true' by default, use --no-hotswap to turn off",
                    },
                    'hotswap-fallback': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'which skips CloudFormation and updates the resources directly, ' +
                            'and falls back to a full deployment if that is not possible.',
                    },
                    'hotswap-ecs-minimum-healthy-percent': {
                        type: 'string',
                        desc: 'Lower limit on the number of your service\'s tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-maximum-healthy-percent': {
                        type: 'string',
                        desc: 'Upper limit on the number of your service\'s tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-stabilization-timeout-seconds': {
                        type: 'string',
                        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
                    },
                    'logs': {
                        type: 'boolean',
                        default: true,
                        desc: 'Show CloudWatch log events from all resources in the selected Stacks in the terminal. ' +
                            "'true' by default, use --no-logs to turn off",
                    },
                    'concurrency': { type: 'number', desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.', default: 1, requiresArg: true },
                },
            },
            destroy: {
                description: 'Destroy the stack(s) named STACKS',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    all: { type: 'boolean', default: false, desc: 'Destroy all available stacks' },
                    exclusively: { type: 'boolean', alias: 'e', desc: 'Only destroy requested stacks, don\'t include dependees' },
                    force: { type: 'boolean', alias: 'f', desc: 'Do not ask for confirmation before destroying the stacks' },
                },
            },
            diff: {
                description: 'Compares the specified stack with the deployed stack or a local template file, and returns with status 1 if any difference is found',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only diff requested stacks, don\'t include dependencies' },
                    'context-lines': { type: 'number', desc: 'Number of context lines to include in arbitrary JSON diff rendering', default: 3, requiresArg: true },
                    'template': { type: 'string', desc: 'The path to the CloudFormation template to compare with', requiresArg: true },
                    'strict': { type: 'boolean', desc: 'Do not filter out AWS::CDK::Metadata resources, mangled non-ASCII characters, or the CheckBootstrapVersionRule', default: false },
                    'security-only': { type: 'boolean', desc: 'Only diff for broadened security changes', default: false },
                    'fail': { type: 'boolean', desc: 'Fail with exit code 1 in case of diff' },
                    'processed': { type: 'boolean', desc: 'Whether to compare against the template with Transforms already processed', default: false },
                    'quiet': { type: 'boolean', alias: 'q', desc: 'Do not print stack name and default message when there is no diff to stdout', default: false },
                    'change-set': { type: 'boolean', alias: 'changeset', desc: 'Whether to create a changeset to analyze resource replacements. In this mode, diff will use the deploy role instead of the lookup role.', default: true },
                    'import-existing-resources': { type: 'boolean', desc: 'Whether or not the change set imports resources that already exist', default: false },
                },
            },
            drift: {
                description: 'Detect drifts in the given CloudFormation stack(s)',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    fail: { type: 'boolean', desc: 'Fail with exit code 1 if drift is detected' },
                },
            },
            metadata: {
                description: 'Returns all metadata associated with this stack',
                arg: {
                    name: 'STACK',
                    variadic: false,
                },
            },
            acknowledge: {
                aliases: ['ack'],
                description: 'Acknowledge a notice so that it does not show up anymore',
                arg: {
                    name: 'ID',
                    variadic: false,
                },
            },
            notices: {
                description: 'Returns a list of relevant notices',
                options: {
                    unacknowledged: { type: 'boolean', alias: 'u', default: false, desc: 'Returns a list of unacknowledged notices' },
                },
            },
            init: {
                description: 'Create a new, empty CDK project from a template.',
                arg: {
                    name: 'TEMPLATE',
                    variadic: false,
                },
                options: {
                    'language': { type: 'string', alias: 'l', desc: 'The language to be used for the new project (default can be configured in ~/.cdk.json)', choices: await (0, init_1.availableInitLanguages)() },
                    'list': { type: 'boolean', desc: 'List the available templates' },
                    'generate-only': { type: 'boolean', default: false, desc: 'If true, only generates project files, without executing additional operations such as setting up a git repo, installing dependencies or compiling the project' },
                    'lib-version': { type: 'string', alias: 'V', default: undefined, desc: 'The version of the CDK library (aws-cdk-lib) to initialize the project with. Defaults to the version that was current when this CLI was built.' },
                },
            },
            migrate: {
                description: 'Migrate existing AWS resources into a CDK app',
                options: {
                    'stack-name': { type: 'string', alias: 'n', desc: 'The name assigned to the stack created in the new project. The name of the app will be based off this name as well.', requiresArg: true },
                    'language': { type: 'string', default: 'typescript', alias: 'l', desc: 'The language to be used for the new project', choices: cdk_from_cfn.supported_languages() },
                    'account': { type: 'string', desc: 'The account to retrieve the CloudFormation stack template from' },
                    'region': { type: 'string', desc: 'The region to retrieve the CloudFormation stack template from' },
                    'from-path': { type: 'string', desc: 'The path to the CloudFormation template to migrate. Use this for locally stored templates' },
                    'from-stack': { type: 'boolean', desc: 'Use this flag to retrieve the template for an existing CloudFormation stack' },
                    'output-path': { type: 'string', desc: 'The output path for the migrated CDK app' },
                    'from-scan': {
                        type: 'string',
                        desc: 'Determines if a new scan should be created, or the last successful existing scan should be used ' +
                            '\n options are "new" or "most-recent"',
                    },
                    'filter': {
                        type: 'array',
                        desc: 'Filters the resource scan based on the provided criteria in the following format: "key1=value1,key2=value2"' +
                            '\n This field can be passed multiple times for OR style filtering: ' +
                            '\n filtering options: ' +
                            '\n resource-identifier: A key-value pair that identifies the target resource. i.e. {"ClusterName", "myCluster"}' +
                            '\n resource-type-prefix: A string that represents a type-name prefix. i.e. "AWS::DynamoDB::"' +
                            '\n tag-key: a string that matches resources with at least one tag with the provided key. i.e. "myTagKey"' +
                            '\n tag-value: a string that matches resources with at least one tag with the provided value. i.e. "myTagValue"',
                    },
                    'compress': { type: 'boolean', desc: 'Use this flag to zip the generated CDK app' },
                },
            },
            context: {
                description: 'Manage cached context values',
                options: {
                    reset: { alias: 'e', desc: 'The context key (or its index) to reset', type: 'string', requiresArg: true, default: undefined },
                    force: { alias: 'f', desc: 'Ignore missing key error', type: 'boolean', default: false },
                    clear: { desc: 'Clear all context', type: 'boolean', default: false },
                },
            },
            docs: {
                aliases: ['doc'],
                description: 'Opens the reference documentation in a browser',
                options: {
                    browser: {
                        alias: 'b',
                        desc: 'the command to use to open the browser, using %u as a placeholder for the path of the file to open',
                        type: 'string',
                        default: exports.YARGS_HELPERS.browserForPlatform(),
                    },
                },
            },
            doctor: {
                description: 'Check your set-up for potential problems',
            },
            refactor: {
                description: 'Moves resources between stacks or within the same stack',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'dry-run': {
                        type: 'boolean',
                        desc: 'Do not perform any changes, just show what would be done',
                        default: false,
                    },
                    'exclude-file': {
                        type: 'string',
                        requiresArg: true,
                        desc: 'If specified, CDK will use the given file to exclude resources from the refactor',
                    },
                    'mapping-file': {
                        type: 'string',
                        requiresArg: true,
                        desc: 'A file that declares an explicit mapping to be applied. If provided, the command will use it instead of computing the mapping.',
                    },
                    'revert': {
                        type: 'boolean',
                        default: false,
                        desc: 'If specified, the command will revert the refactor operation. This is only valid if a mapping file was provided.',
                    },
                },
            },
        },
    };
}
//# sourceMappingURL=data:application/json;base64,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