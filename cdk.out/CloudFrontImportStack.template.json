{"Description": "Import existing CloudFront distributions from Terraform for migration to CDK", "Outputs": {"ExistingApiGatewayDist1Id": {"Description": "Existing API Gateway Distribution 1 ID (E1Y2GZXHXCGI8H)", "Value": "E1Y2GZXHXCGI8H"}, "ExistingApiGatewayDist2Id": {"Description": "Existing API Gateway Distribution 2 ID (E131RZ69QYLX7O)", "Value": "E131RZ69QYLX7O"}, "ExistingApiGatewayDist3Id": {"Description": "Existing API Gateway Distribution 3 ID (E2EZONTUD8DA1O)", "Value": "E2EZONTUD8DA1O"}, "ExistingExternalWebsiteDistId": {"Description": "Existing External Website Distribution ID (EXZC66UKJAABK)", "Value": "EXZC66UKJAABK"}, "ExistingComplexDistId": {"Description": "Existing Complex Distribution ID (ES27SJ7X3UX1M)", "Value": "ES27SJ7X3UX1M"}, "ExistingMultiOriginDistId": {"Description": "Existing Multi-Origin Distribution ID (E10NZFW9D6AMU2)", "Value": "E10NZFW9D6AMU2"}, "ExistingS3WebsiteDistId": {"Description": "Existing S3 Website Distribution ID (E1YNWVHTAGY30T)", "Value": "E1YNWVHTAGY30T"}, "ExistingCertificateArn": {"Description": "Existing ACM Certificate ARN", "Value": "arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196"}, "ExistingS3BucketName": {"Description": "Existing S3 Bucket Name", "Value": "deleteonly-dev"}}, "Resources": {"TerraformMigrationStatus24AF15F5": {"Type": "AWS::SSM::Parameter", "Properties": {"Description": "Status of Terraform to CDK migration for CloudFront distributions", "Name": "/cloudfront/terraform-migration/status", "Type": "String", "Value": "imported-existing-resources"}, "Metadata": {"aws:cdk:path": "CloudFrontImportStack/TerraformMigrationStatus/Resource"}}, "DistributionInventoryB0010989": {"Type": "AWS::SSM::Parameter", "Properties": {"Description": "Number of CloudFront distributions imported from Terraform", "Name": "/cloudfront/terraform-migration/distribution-count", "Type": "String", "Value": "7"}, "Metadata": {"aws:cdk:path": "CloudFrontImportStack/DistributionInventory/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/52OQQ6CMBREz0KXpnyh7FziXg0mbggxtXxNgRbS38qCcHcDBg/gamZeZpIRIJIE0kiOFKu6jTv9gOnqpWp5gdQHp7CsuBzpPlEGeVAt+lzSAonMUnXavi7SSYMeXTmxYfMnaZAd2I5xRmvrJruwkRpJOT143duVzPzvZcWPT/t7MM9LPAc/BD9z29cIDe3fQkCaQRI1pHXsgvXaIBRf/QAb1Pi+BAEAAA=="}, "Metadata": {"aws:cdk:path": "CloudFrontImportStack/CDKMetadata/Default"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}