{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.200.1"}, "children": {"AwsmcpdemoStack": {"id": "AwsmcpdemoStack", "path": "AwsmcpdemoStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"CDKMetadata": {"id": "CDKMetadata", "path": "AwsmcpdemoStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AwsmcpdemoStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "AwsmcpdemoStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "AwsmcpdemoStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "ApiGatewayDistributionStack": {"id": "ApiGatewayDistributionStack", "path": "ApiGatewayDistributionStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"ApiGatewayCache1": {"id": "ApiGatewayCache1", "path": "ApiGatewayDistributionStack/ApiGatewayCache1", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayCache1/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "ApiGatewayCustomCachePolicy1", "comment": "Custom cache policy for API Gateway distribution 1", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "whitelist", "cookies": ["Accept", "Origin", "<PERSON><PERSON><PERSON>"]}, "headersConfig": {"headerBehavior": "whitelist", "headers": ["*"]}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "none"}}}}}}}}, "ApiGatewayDistribution1": {"id": "ApiGatewayDistribution1", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution1", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http1.1", "priceClass": "PriceClass_All", "enableIpv6": false, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}]}, "children": {"Origin1": {"id": "Origin1", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution1/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution1/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "p0z7suimt8.execute-api.us-east-1.amazonaws.com", "id": "ApiGatewayDistributionStackApiGatewayDistribution1Origin1917ECBC9", "originPath": "/dev", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution1Origin1917ECBC9", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "ApiGatewayCache11DB2D25A"}, "compress": false, "viewerProtocolPolicy": "https-only"}, "aliases": [], "comment": "API Gateway Distribution - E1Y2GZXHXCGI8H Recreation", "httpVersion": "http1.1", "ipv6Enabled": false, "priceClass": "PriceClass_All"}}}}}}, "ApiGatewayCache2": {"id": "ApiGatewayCache2", "path": "ApiGatewayDistributionStack/ApiGatewayCache2", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayCache2/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "ApiGatewayCustomCachePolicy2", "comment": "Custom cache policy for API Gateway distribution 2", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "none"}, "headersConfig": {"headerBehavior": "whitelist", "headers": ["CloudFront-Is-Tablet-Viewer", "CloudFront-Is-Mobile-Viewer", "CloudFront-Is-SmartTV-Viewer", "CloudFront-Is-Desktop-Viewer"]}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "none"}}}}}}}}, "JpgCachePolicy": {"id": "JpgCachePolicy", "path": "ApiGatewayDistributionStack/JpgCachePolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/JpgCachePolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "JpgFilesCachePolicy", "comment": "Cache policy for JPG files", "minTtl": 0, "maxTtl": 31536000, "defaultTtl": 86400, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "none"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "none"}}}}}}}}, "ApiGatewayDistribution2": {"id": "ApiGatewayDistribution2", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution2", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http1.1", "priceClass": "PriceClass_All", "enableIpv6": false, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "additionalBehaviors": "*", "errorResponses": [{"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}], "certificate": "*", "domainNames": "*"}, {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}]}, "children": {"Origin1": {"id": "Origin1", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution2/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin2": {"id": "Origin2", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution2/Origin2", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution2/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "20rhz2v7tc.execute-api.us-west-2.amazonaws.com", "id": "ApiGatewayDistributionStackApiGatewayDistribution2Origin14B834861", "originPath": "/prod/regionTest", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}, {"domainName": "20rhz2v7tc.execute-api.us-west-2.amazonaws.com", "id": "ApiGatewayDistributionStackApiGatewayDistribution2Origin26D325D9E", "originPath": "/prod/regionTest", "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only"}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution2Origin14B834861", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "ApiGatewayCache2A091AD93"}, "compress": false, "viewerProtocolPolicy": "https-only"}, "aliases": [], "cacheBehaviors": [{"pathPattern": "Dir*/*.jpg", "targetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution2Origin26D325D9E", "allowedMethods": ["GET", "HEAD"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "JpgCachePolicy8CC17EDB"}, "compress": false, "viewerProtocolPolicy": "allow-all"}], "comment": "API Gateway Distribution with Cache Behaviors - E131RZ69QYLX7O Recreation", "customErrorResponses": [{"errorCachingMinTtl": 0, "errorCode": 403}, {"errorCachingMinTtl": 0, "errorCode": 404}, {"errorCachingMinTtl": 0, "errorCode": 502}, {"errorCachingMinTtl": 0, "errorCode": 504}], "httpVersion": "http1.1", "ipv6Enabled": false, "priceClass": "PriceClass_All"}}}}}}, "ApiGatewayCache3": {"id": "ApiGatewayCache3", "path": "ApiGatewayDistributionStack/ApiGatewayCache3", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayCache3/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "ApiGatewayCustomCachePolicy3", "comment": "Custom cache policy for API Gateway distribution 3", "minTtl": 0, "maxTtl": 31536000, "defaultTtl": 86400, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "none"}, "headersConfig": {"headerBehavior": "whitelist", "headers": ["CloudFront-Viewer-Country", "CloudFront-Forwarded-Proto", "CloudFront-Is-Tablet-Viewer", "CloudFront-Is-Mobile-Viewer", "User-Agent", "CloudFront-Is-SmartTV-Viewer", "CloudFront-Is-Desktop-Viewer"]}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "none"}}}}}}}}, "ApiGatewayDistribution3": {"id": "ApiGatewayDistribution3", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution3", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}]}, "children": {"Origin1": {"id": "Origin1", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution3/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution3/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "9uerlzte0e.execute-api.us-east-1.amazonaws.com", "id": "ApiGatewayDistributionStackApiGatewayDistribution3Origin1A7666033", "originPath": "/test/testedge", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution3Origin1A7666033", "allowedMethods": ["GET", "HEAD"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "ApiGatewayCache31542FF1E"}, "compress": false, "viewerProtocolPolicy": "allow-all"}, "aliases": [], "comment": "API Gateway Distribution with Device Detection - E2EZONTUD8DA1O Recreation", "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_All"}}}}}}, "ApiGatewayDistribution1DomainName": {"id": "ApiGatewayDistribution1DomainName", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution1DomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ApiGatewayDistribution2DomainName": {"id": "ApiGatewayDistribution2DomainName", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution2DomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ApiGatewayDistribution3DomainName": {"id": "ApiGatewayDistribution3DomainName", "path": "ApiGatewayDistributionStack/ApiGatewayDistribution3DomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "ApiGatewayDistributionStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "ApiGatewayDistributionStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "ApiGatewayDistributionStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "ApiGatewayDistributionStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "S3WebsiteDistributionStack": {"id": "S3WebsiteDistributionStack", "path": "S3WebsiteDistributionStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"CloudFrontWebACL": {"id": "CloudFrontWebACL", "path": "S3WebsiteDistributionStack/CloudFrontWebACL", "constructInfo": {"fqn": "aws-cdk-lib.aws_wafv2.CfnWebACL", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::WAFv2::WebACL", "aws:cdk:cloudformation:props": {"defaultAction": {"allow": {}}, "description": "Web ACL for Mikes Website CloudFront Distribution", "name": "CreatedByCloudFront-MikesWebsite-WebACL", "rules": [{"name": "AWSManagedRulesCommonRuleSet", "priority": 1, "overrideAction": {"none": {}}, "statement": {"managedRuleGroupStatement": {"vendorName": "AWS", "name": "AWSManagedRulesCommonRuleSet"}}, "visibilityConfig": {"sampledRequestsEnabled": true, "cloudWatchMetricsEnabled": true, "metricName": "CommonRuleSetMetric"}}, {"name": "AWSManagedRulesKnownBadInputsRuleSet", "priority": 2, "overrideAction": {"none": {}}, "statement": {"managedRuleGroupStatement": {"vendorName": "AWS", "name": "AWSManagedRulesKnownBadInputsRuleSet"}}, "visibilityConfig": {"sampledRequestsEnabled": true, "cloudWatchMetricsEnabled": true, "metricName": "KnownBadInputsRuleSetMetric"}}], "scope": "CLOUDFRONT", "visibilityConfig": {"sampledRequestsEnabled": true, "cloudWatchMetricsEnabled": true, "metricName": "MikesWebsiteWebACL"}}}}, "CachingOptimized": {"id": "CachingOptimized", "path": "S3WebsiteDistributionStack/CachingOptimized", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "S3WebsiteDistribution": {"id": "S3WebsiteDistribution", "path": "S3WebsiteDistributionStack/S3WebsiteDistribution", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "webAclId": "*", "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}]}, "children": {"Origin1": {"id": "Origin1", "path": "S3WebsiteDistributionStack/S3WebsiteDistribution/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "S3WebsiteDistributionStack/S3WebsiteDistribution/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "mikeswebsite.s3-website-us-east-1.amazonaws.com", "id": "S3WebsiteDistributionStackS3WebsiteDistributionOrigin176381268", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "http-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "S3WebsiteDistributionStackS3WebsiteDistributionOrigin176381268", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "compress": true, "viewerProtocolPolicy": "https-only"}, "aliases": [], "comment": "S3 Website Distribution - E1YNWVHTAGY30T Recreation", "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_All", "webAclId": {"Fn::GetAtt": ["CloudFrontWebACL", "<PERSON><PERSON>"]}}}}}}}, "ExternalWebsiteCache": {"id": "ExternalWebsiteCache", "path": "S3WebsiteDistributionStack/ExternalWebsiteCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "S3WebsiteDistributionStack/ExternalWebsiteCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "ExternalWebsiteCachePolicy", "comment": "Custom cache policy for external website", "minTtl": 0, "maxTtl": 86400, "defaultTtl": 86400, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "none"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "none"}}}}}}}}, "ExternalWebsiteDistribution": {"id": "ExternalWebsiteDistribution", "path": "S3WebsiteDistributionStack/ExternalWebsiteDistribution", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}]}, "children": {"Origin1": {"id": "Origin1", "path": "S3WebsiteDistributionStack/ExternalWebsiteDistribution/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "S3WebsiteDistributionStack/ExternalWebsiteDistribution/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "quotewizard.com", "id": "S3WebsiteDistributionStackExternalWebsiteDistributionOrigin1FAA4C476", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "S3WebsiteDistributionStackExternalWebsiteDistributionOrigin1FAA4C476", "allowedMethods": ["GET", "HEAD"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "ExternalWebsiteCache81929B10"}, "compress": false, "viewerProtocolPolicy": "allow-all"}, "aliases": [], "comment": "External Website Distribution - EXZC66UKJAABK Recreation", "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_All"}}}}}}, "MultiOriginCache": {"id": "MultiOriginCache", "path": "S3WebsiteDistributionStack/MultiOriginCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "S3WebsiteDistributionStack/MultiOriginCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "MultiOriginCachePolicy", "comment": "Custom cache policy for multi-origin distribution", "minTtl": 0, "maxTtl": 31536000, "defaultTtl": 86400, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "all"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "all"}}}}}}}}, "MultiOriginDistribution": {"id": "MultiOriginDistribution", "path": "S3WebsiteDistributionStack/MultiOriginDistribution", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}]}, "children": {"Origin1": {"id": "Origin1", "path": "S3WebsiteDistributionStack/MultiOriginDistribution/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "S3WebsiteDistributionStack/MultiOriginDistribution/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "cbth.quotewizard.com", "id": "S3WebsiteDistributionStackMultiOriginDistributionOrigin14C00D240", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "S3WebsiteDistributionStackMultiOriginDistributionOrigin14C00D240", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "MultiOriginCache2868F312"}, "compress": false, "viewerProtocolPolicy": "redirect-to-https"}, "aliases": [], "comment": "Multi-Origin Distribution - E10NZFW9D6AMU2 Recreation", "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_All"}}}}}}, "S3WebsiteDistributionDomainName": {"id": "S3WebsiteDistributionDomainName", "path": "S3WebsiteDistributionStack/S3WebsiteDistributionDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExternalWebsiteDistributionDomainName": {"id": "ExternalWebsiteDistributionDomainName", "path": "S3WebsiteDistributionStack/ExternalWebsiteDistributionDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "MultiOriginDistributionDomainName": {"id": "MultiOriginDistributionDomainName", "path": "S3WebsiteDistributionStack/MultiOriginDistributionDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "WebACLArn": {"id": "WebACLArn", "path": "S3WebsiteDistributionStack/WebACLArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "S3WebsiteDistributionStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "S3WebsiteDistributionStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "S3WebsiteDistributionStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "S3WebsiteDistributionStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "ComplexDistributionStack": {"id": "ComplexDistributionStack", "path": "ComplexDistributionStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"DeltyTestCertificate": {"id": "DeltyTestCertificate", "path": "ComplexDistributionStack/DeltyTestCertificate", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "PublisherPortalCache": {"id": "PublisherPortalCache", "path": "ComplexDistributionStack/PublisherPortalCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ComplexDistributionStack/PublisherPortalCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "PublisherPortalCachePolicy", "comment": "Cache policy for publisher portal", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "all"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "all"}}}}}}}}, "DeleteOnlyDevBucket": {"id": "DeleteOnlyDevBucket", "path": "ComplexDistributionStack/DeleteOnlyDevBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.200.1", "metadata": []}}, "SftpHandlerCache": {"id": "SftpHandlerCache", "path": "ComplexDistributionStack/SftpHandlerCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ComplexDistributionStack/SftpHandlerCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "SftpHandlerCachePolicy", "comment": "Cache policy for SFTP handler", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "all"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "all"}}}}}}}}, "BulkEditCache": {"id": "BulkEditCache", "path": "ComplexDistributionStack/BulkEditCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ComplexDistributionStack/BulkEditCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "BulkEditCachePolicy", "comment": "Cache policy for bulk edit", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "all"}, "headersConfig": {"headerBehavior": "none"}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "all"}}}}}}}}, "KubeCache": {"id": "<PERSON><PERSON><PERSON><PERSON>", "path": "ComplexDistributionStack/KubeCache", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CachePolicy", "version": "2.200.1", "metadata": [{"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "ComplexDistributionStack/KubeCache/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCachePolicy", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CachePolicy", "aws:cdk:cloudformation:props": {"cachePolicyConfig": {"name": "KubeCachePolicy", "comment": "Cache policy for kube endpoints", "minTtl": 0, "maxTtl": 0, "defaultTtl": 0, "parametersInCacheKeyAndForwardedToOrigin": {"cookiesConfig": {"cookieBehavior": "all"}, "headersConfig": {"headerBehavior": "whitelist", "headers": ["Authorization", "Origin", "Accept-Encoding"]}, "enableAcceptEncodingGzip": false, "enableAcceptEncodingBrotli": false, "queryStringsConfig": {"queryStringBehavior": "all"}}}}}}}}, "ComplexDistribution": {"id": "ComplexDistribution", "path": "ComplexDistributionStack/ComplexDistribution", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.200.1", "metadata": [{"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "certificate": "*", "domainNames": "*", "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}, "additionalBehaviors": "*", "errorResponses": [{"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}]}, {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}, {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}]}, {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}]}, "children": {"Origin1": {"id": "Origin1", "path": "ComplexDistributionStack/ComplexDistribution/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin2": {"id": "Origin2", "path": "ComplexDistributionStack/ComplexDistribution/Origin2", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"S3Origin": {"id": "S3Origin", "path": "ComplexDistributionStack/ComplexDistribution/Origin2/S3Origin", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.OriginAccessIdentity", "version": "2.200.1", "metadata": [{}]}, "children": {"Resource": {"id": "Resource", "path": "ComplexDistributionStack/ComplexDistribution/Origin2/S3Origin/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCloudFrontOriginAccessIdentity", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CloudFrontOriginAccessIdentity", "aws:cdk:cloudformation:props": {"cloudFrontOriginAccessIdentityConfig": {"comment": "Identity for ComplexDistributionStackComplexDistributionOrigin22C9F7CB1"}}}}}}}}, "Origin3": {"id": "Origin3", "path": "ComplexDistributionStack/ComplexDistribution/Origin3", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin4": {"id": "Origin4", "path": "ComplexDistributionStack/ComplexDistribution/Origin4", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Resource": {"id": "Resource", "path": "ComplexDistributionStack/ComplexDistribution/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": "dqhg2xtwi2.execute-api.us-east-1.amazonaws.com", "id": "ComplexDistributionStackComplexDistributionOrigin16CC7FE64", "originPath": "/dev/portal", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 60, "originKeepaliveTimeout": 5}}, {"domainName": "deleteonly-dev.s3.us-east-1.amazonaws.com", "id": "ComplexDistributionStackComplexDistributionOrigin22C9F7CB1", "s3OriginConfig": {"originAccessIdentity": {"Fn::Join": ["", ["origin-access-identity/cloudfront/", {"Ref": "ComplexDistributionOrigin2S3Origin5D9E4DD2"}]]}}}, {"domainName": "s3.amazonaws.com", "id": "ComplexDistributionStackComplexDistributionOrigin3CA4983F9", "originPath": "/deleteonly-dev", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 30, "originKeepaliveTimeout": 5}}, {"domainName": "dqhg2xtwi2.execute-api.us-east-1.amazonaws.com", "id": "ComplexDistributionStackComplexDistributionOrigin44AF468E7", "originPath": "/dev", "connectionAttempts": 3, "connectionTimeout": 10, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only", "httpPort": 80, "httpsPort": 443, "originReadTimeout": 60, "originKeepaliveTimeout": 60}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "ComplexDistributionStackComplexDistributionOrigin16CC7FE64", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "PublisherPortalCache8BEB5AAB"}, "compress": true, "viewerProtocolPolicy": "redirect-to-https"}, "aliases": ["test-publisher.delty.com"], "cacheBehaviors": [{"pathPattern": "/sftphandler/*", "targetOriginId": "ComplexDistributionStackComplexDistributionOrigin22C9F7CB1", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "SftpHandlerCacheD292D7B8"}, "compress": false, "viewerProtocolPolicy": "redirect-to-https"}, {"pathPattern": "/bulkedit/*", "targetOriginId": "ComplexDistributionStackComplexDistributionOrigin3CA4983F9", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "BulkEditCacheFC5EA5C2"}, "compress": true, "viewerProtocolPolicy": "redirect-to-https"}, {"pathPattern": "kube*", "targetOriginId": "ComplexDistributionStackComplexDistributionOrigin44AF468E7", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachedMethods": ["GET", "HEAD"], "cachePolicyId": {"Ref": "KubeCacheAC2B3D63"}, "compress": false, "viewerProtocolPolicy": "redirect-to-https"}], "comment": "Complex Multi-Origin Distribution - ES27SJ7X3UX1M Recreation", "customErrorResponses": [{"errorCachingMinTtl": 0, "errorCode": 403}, {"errorCachingMinTtl": 0, "errorCode": 404}, {"errorCachingMinTtl": 0, "errorCode": 500}, {"errorCachingMinTtl": 0, "errorCode": 501}, {"errorCachingMinTtl": 0, "errorCode": 502}, {"errorCachingMinTtl": 0, "errorCode": 503}, {"errorCachingMinTtl": 0, "errorCode": 504}], "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_All", "viewerCertificate": {"acmCertificateArn": "arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196", "minimumProtocolVersion": "TLSv1.2_2021", "sslSupportMethod": "sni-only"}}}}}}}, "ComplexDistributionDomainName": {"id": "ComplexDistributionDomainName", "path": "ComplexDistributionStack/ComplexDistributionDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ComplexDistributionId": {"id": "ComplexDistributionId", "path": "ComplexDistributionStack/ComplexDistributionId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "CustomDomainName": {"id": "CustomDomainName", "path": "ComplexDistributionStack/CustomDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "ComplexDistributionStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "ComplexDistributionStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "ComplexDistributionStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "ComplexDistributionStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}