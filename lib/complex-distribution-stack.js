"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplexDistributionStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront = __importStar(require("aws-cdk-lib/aws-cloudfront"));
const origins = __importStar(require("aws-cdk-lib/aws-cloudfront-origins"));
const certificatemanager = __importStar(require("aws-cdk-lib/aws-certificatemanager"));
const s3 = __importStar(require("aws-cdk-lib/aws-s3"));
class ComplexDistributionStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Import the existing ACM certificate
        const certificate = certificatemanager.Certificate.fromCertificateArn(this, 'DeltyTestCertificate', 'arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196');
        // Distribution ES27SJ7X3UX1M - Complex multi-origin distribution with custom domain
        const complexDistribution = new cloudfront.Distribution(this, 'ComplexDistribution', {
            comment: 'Complex Multi-Origin Distribution - ES27SJ7X3UX1M Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: true,
            certificate: certificate,
            domainNames: ['test-publisher.delty.com'],
            // Default behavior - Publisher Portal
            defaultBehavior: {
                origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
                    originPath: '/dev/portal',
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(60),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: true,
                cachePolicy: new cloudfront.CachePolicy(this, 'PublisherPortalCache', {
                    cachePolicyName: 'PublisherPortalCachePolicy',
                    comment: 'Cache policy for publisher portal',
                    defaultTtl: cdk.Duration.seconds(0),
                    maxTtl: cdk.Duration.seconds(0),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.all(),
                }),
            },
            additionalBehaviors: {
                // SFTP Handler behavior - S3 origin
                '/sftphandler/*': {
                    origin: new origins.S3Origin(
                    // Note: This references an existing S3 bucket
                    // You may need to import or create this bucket separately
                    s3.Bucket.fromBucketName(this, 'DeleteOnlyDevBucket', 'deleteonly-dev')),
                    viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                    allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                    cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                    compress: false,
                    cachePolicy: new cloudfront.CachePolicy(this, 'SftpHandlerCache', {
                        cachePolicyName: 'SftpHandlerCachePolicy',
                        comment: 'Cache policy for SFTP handler',
                        defaultTtl: cdk.Duration.seconds(0),
                        maxTtl: cdk.Duration.seconds(0),
                        minTtl: cdk.Duration.seconds(0),
                        headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                        queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
                        cookieBehavior: cloudfront.CacheCookieBehavior.all(),
                    }),
                },
                // Bulk Edit behavior - S3 custom origin
                '/bulkedit/*': {
                    origin: new origins.HttpOrigin('s3.amazonaws.com', {
                        originPath: '/deleteonly-dev',
                        protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                        httpsPort: 443,
                        httpPort: 80,
                        readTimeout: cdk.Duration.seconds(30),
                        keepaliveTimeout: cdk.Duration.seconds(5),
                        connectionAttempts: 3,
                        connectionTimeout: cdk.Duration.seconds(10),
                    }),
                    viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                    allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                    cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                    compress: true,
                    cachePolicy: new cloudfront.CachePolicy(this, 'BulkEditCache', {
                        cachePolicyName: 'BulkEditCachePolicy',
                        comment: 'Cache policy for bulk edit',
                        defaultTtl: cdk.Duration.seconds(0),
                        maxTtl: cdk.Duration.seconds(0),
                        minTtl: cdk.Duration.seconds(0),
                        headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                        queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
                        cookieBehavior: cloudfront.CacheCookieBehavior.all(),
                    }),
                },
                // Kube behavior - Publisher Kube API
                'kube*': {
                    origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
                        originPath: '/dev',
                        protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                        httpsPort: 443,
                        httpPort: 80,
                        readTimeout: cdk.Duration.seconds(60),
                        keepaliveTimeout: cdk.Duration.seconds(60),
                        connectionAttempts: 3,
                        connectionTimeout: cdk.Duration.seconds(10),
                    }),
                    viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                    allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                    cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                    compress: false,
                    cachePolicy: new cloudfront.CachePolicy(this, 'KubeCache', {
                        cachePolicyName: 'KubeCachePolicy',
                        comment: 'Cache policy for kube endpoints',
                        defaultTtl: cdk.Duration.seconds(0),
                        maxTtl: cdk.Duration.seconds(0),
                        minTtl: cdk.Duration.seconds(0),
                        headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Authorization', 'Origin', 'Accept-Encoding'),
                        queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
                        cookieBehavior: cloudfront.CacheCookieBehavior.all(),
                    }),
                },
            },
            // Custom error responses
            errorResponses: [
                {
                    httpStatus: 403,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 404,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 500,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 501,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 502,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 503,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 504,
                    ttl: cdk.Duration.seconds(0),
                },
            ],
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Output the distribution information
        new cdk.CfnOutput(this, 'ComplexDistributionDomainName', {
            value: complexDistribution.distributionDomainName,
            description: 'CloudFront domain name for complex distribution',
        });
        new cdk.CfnOutput(this, 'ComplexDistributionId', {
            value: complexDistribution.distributionId,
            description: 'CloudFront distribution ID for complex distribution',
        });
        new cdk.CfnOutput(this, 'CustomDomainName', {
            value: 'test-publisher.delty.com',
            description: 'Custom domain name for the distribution',
        });
    }
}
exports.ComplexDistributionStack = ComplexDistributionStack;
//# sourceMappingURL=data:application/json;base64,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