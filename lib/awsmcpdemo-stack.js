"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AwsmcpdemoStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
class AwsmcpdemoStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // This is a placeholder stack - individual distribution stacks are in separate files
        // See:
        // - ApiGatewayDistributionStack for API Gateway distributions
        // - S3WebsiteDistributionStack for S3 website distributions
        // - ComplexDistributionStack for multi-origin distributions
    }
}
exports.AwsmcpdemoStack = AwsmcpdemoStack;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXdzbWNwZGVtby1zdGFjay5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImF3c21jcGRlbW8tc3RhY2sudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpREFBbUM7QUFPbkMsTUFBYSxlQUFnQixTQUFRLEdBQUcsQ0FBQyxLQUFLO0lBQzVDLFlBQVksS0FBZ0IsRUFBRSxFQUFVLEVBQUUsS0FBc0I7UUFDOUQsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFeEIscUZBQXFGO1FBQ3JGLE9BQU87UUFDUCw4REFBOEQ7UUFDOUQsNERBQTREO1FBQzVELDREQUE0RDtJQUM5RCxDQUFDO0NBQ0Y7QUFWRCwwQ0FVQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGNkayBmcm9tICdhd3MtY2RrLWxpYic7XG5pbXBvcnQgeyBDb25zdHJ1Y3QgfSBmcm9tICdjb25zdHJ1Y3RzJztcbmltcG9ydCAqIGFzIGNsb3VkZnJvbnQgZnJvbSAnYXdzLWNkay1saWIvYXdzLWNsb3VkZnJvbnQnO1xuaW1wb3J0ICogYXMgb3JpZ2lucyBmcm9tICdhd3MtY2RrLWxpYi9hd3MtY2xvdWRmcm9udC1vcmlnaW5zJztcbmltcG9ydCAqIGFzIHMzIGZyb20gJ2F3cy1jZGstbGliL2F3cy1zMyc7XG5pbXBvcnQgKiBhcyBjZXJ0aWZpY2F0ZW1hbmFnZXIgZnJvbSAnYXdzLWNkay1saWIvYXdzLWNlcnRpZmljYXRlbWFuYWdlcic7XG5cbmV4cG9ydCBjbGFzcyBBd3NtY3BkZW1vU3RhY2sgZXh0ZW5kcyBjZGsuU3RhY2sge1xuICBjb25zdHJ1Y3RvcihzY29wZTogQ29uc3RydWN0LCBpZDogc3RyaW5nLCBwcm9wcz86IGNkay5TdGFja1Byb3BzKSB7XG4gICAgc3VwZXIoc2NvcGUsIGlkLCBwcm9wcyk7XG5cbiAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgc3RhY2sgLSBpbmRpdmlkdWFsIGRpc3RyaWJ1dGlvbiBzdGFja3MgYXJlIGluIHNlcGFyYXRlIGZpbGVzXG4gICAgLy8gU2VlOlxuICAgIC8vIC0gQXBpR2F0ZXdheURpc3RyaWJ1dGlvblN0YWNrIGZvciBBUEkgR2F0ZXdheSBkaXN0cmlidXRpb25zXG4gICAgLy8gLSBTM1dlYnNpdGVEaXN0cmlidXRpb25TdGFjayBmb3IgUzMgd2Vic2l0ZSBkaXN0cmlidXRpb25zXG4gICAgLy8gLSBDb21wbGV4RGlzdHJpYnV0aW9uU3RhY2sgZm9yIG11bHRpLW9yaWdpbiBkaXN0cmlidXRpb25zXG4gIH1cbn1cbiJdfQ==