# Existing CloudFront Distributions Configuration

This document captures the current state of your CloudFront distributions managed by Terraform. This serves as a reference for the migration to CDK.

## Distribution Inventory

| Distribution ID | Domain Name | Status | Comment | Origins |
|----------------|-------------|---------|---------|---------|
| E1Y2GZXHXCGI8H | d2s7hrgrz2s1ba.cloudfront.net | Deployed | API Gateway with custom headers | API Gateway (us-east-1) |
| E131RZ69QYLX7O | dtor21q5y56di.cloudfront.net | Deployed | API Gateway with cache behaviors | API Gateway (us-west-2) |
| EXZC66UKJAABK | d1q1i6afcjz18f.cloudfront.net | Deployed | External website distribution | quotewizard.com |
| E2EZONTUD8DA1O | d6omm258hzaqm.cloudfront.net | Deployed | API Gateway with device detection | API Gateway (us-east-1) |
| ES27SJ7X3UX1M | d121gt36t1o2vg.cloudfront.net | Deployed | Complex multi-origin with custom domain | Multiple origins |
| E10NZFW9D6AMU2 | d1vdttacwqw93n.cloudfront.net | Deployed | Multi-origin distribution | API Gateway + External |
| E1YNWVHTAGY30T | d3tqauws6s7t6o.cloudfront.net | Deployed | S3 website with WAF | S3 Website |

## Detailed Configuration

### Distribution E1Y2GZXHXCGI8H
- **Origin**: p0z7suimt8.execute-api.us-east-1.amazonaws.com/dev
- **Protocol**: HTTPS Only
- **HTTP Version**: HTTP/1.1
- **IPv6**: Disabled
- **Price Class**: All
- **Headers**: Forwards all headers (*)
- **Cookies**: Whitelist (Accept, Origin, Referrer)
- **TTL**: Min=0, Default=0, Max=0

### Distribution E131RZ69QYLX7O
- **Origin**: 20rhz2v7tc.execute-api.us-west-2.amazonaws.com/prod/regionTest
- **Protocol**: HTTPS Only
- **HTTP Version**: HTTP/1.1
- **IPv6**: Disabled
- **Price Class**: All
- **Cache Behaviors**:
  - Default: Device detection headers
  - `Dir*/*.jpg`: Cached for 1 day, max 1 year
- **Error Responses**: 403, 404, 502, 504 (TTL=0)

### Distribution EXZC66UKJAABK
- **Origin**: quotewizard.com
- **Protocol**: HTTPS Only
- **HTTP Version**: HTTP/2
- **IPv6**: Enabled
- **Price Class**: All
- **Methods**: GET, HEAD only
- **TTL**: Min=0, Default=86400, Max=86400

### Distribution E2EZONTUD8DA1O
- **Origin**: 9uerlzte0e.execute-api.us-east-1.amazonaws.com/test/testedge
- **Protocol**: HTTPS Only
- **HTTP Version**: HTTP/2
- **IPv6**: Enabled
- **Price Class**: All
- **Headers**: CloudFront device detection + viewer country
- **TTL**: Min=0, Default=86400, Max=31536000

### Distribution ES27SJ7X3UX1M (Complex)
- **Custom Domain**: test-publisher.delty.com
- **Certificate**: arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196
- **HTTP Version**: HTTP/2
- **IPv6**: Enabled
- **Price Class**: All

**Origins**:
1. **Publisher Portal** (Default): dqhg2xtwi2.execute-api.us-east-1.amazonaws.com/dev/portal
2. **Publisher Kube**: dqhg2xtwi2.execute-api.us-east-1.amazonaws.com/dev
3. **S3 Direct**: deleteonly-dev.s3.us-east-1.amazonaws.com
4. **S3 Custom**: s3.amazonaws.com/deleteonly-dev

**Cache Behaviors**:
- Default: Publisher Portal (TTL=0, compress=true, all methods)
- `/sftphandler/*`: S3 origin (TTL=0, all methods)
- `/bulkedit/*`: S3 custom origin (TTL=0, compress=true, all methods)
- `kube*`: Publisher Kube (TTL=0, custom headers: Authorization, Origin, Accept-Encoding)

**Error Responses**: 403, 404, 500, 501, 502, 503, 504 (all TTL=0)

### Distribution E10NZFW9D6AMU2
- **Default Origin**: cbth.quotewizard.com
- **Secondary Origin**: 80zr9dw926.execute-api.us-east-1.amazonaws.com/dev
- **Protocol**: Redirect to HTTPS
- **HTTP Version**: HTTP/2
- **IPv6**: Enabled
- **TTL**: Min=0, Default=86400, Max=31536000

### Distribution E1YNWVHTAGY30T
- **Origin**: mikeswebsite.s3-website-us-east-1.amazonaws.com
- **Protocol**: HTTP Only (origin), HTTPS Only (viewer)
- **HTTP Version**: HTTP/2
- **IPv6**: Enabled
- **WAF**: arn:aws:wafv2:us-east-1:902537659456:global/webacl/CreatedByCloudFront-c4de54aa-9b17-44f0-bb7b-04b44ef5054b/b3f821ff-e229-4ff8-aa95-5bf7fd197341
- **Cache Policy**: Managed caching optimized (658327ea-f89d-4fab-a63d-7e88639e58f6)
- **Compression**: Enabled

## Associated Resources

### ACM Certificate
- **ARN**: arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196
- **Domain**: test-publisher.delty.com
- **Status**: Issued

### S3 Bucket
- **Name**: deleteonly-dev
- **Region**: us-east-1
- **Used by**: ES27SJ7X3UX1M distribution

### WAF Web ACL
- **ARN**: arn:aws:wafv2:us-east-1:902537659456:global/webacl/CreatedByCloudFront-c4de54aa-9b17-44f0-bb7b-04b44ef5054b/b3f821ff-e229-4ff8-aa95-5bf7fd197341
- **Used by**: E1YNWVHTAGY30T distribution

## Migration Notes

1. **No Resource Creation**: The CDK code imports existing resources without creating new ones
2. **State Preservation**: All configurations are documented to ensure no settings are lost
3. **Terraform State**: Keep Terraform state files until migration is complete
4. **Testing**: Verify all distributions work correctly after CDK deployment
5. **DNS**: Custom domain (test-publisher.delty.com) requires DNS validation

## Next Steps for Migration

1. Deploy the CDK import stack to establish resource references
2. Gradually replace Terraform resources with CDK-managed ones
3. Update DNS records if needed
4. Remove Terraform state after successful migration
5. Implement CDK-based CI/CD pipelines
