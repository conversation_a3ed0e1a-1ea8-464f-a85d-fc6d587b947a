#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { AwsmcpdemoStack } from '../lib/awsmcpdemo-stack';

const app = new cdk.App();

// Environment configuration for your AWS account
const env = {
  account: '************',
  region: 'us-east-1',
};

// CloudFront distributions stack - recreates all existing distributions
new AwsmcpdemoStack(app, 'CloudFrontDistributionsStack', {
  env: env,
  description: 'CloudFront distributions recreating existing AWS infrastructure',
  tags: {
    Project: 'AWS MCP Demo',
    Environment: 'Development',
    ManagedBy: 'CDK',
  },
});