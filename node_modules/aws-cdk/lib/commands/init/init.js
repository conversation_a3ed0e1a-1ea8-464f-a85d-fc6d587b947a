"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InitTemplate = void 0;
exports.cliInit = cliInit;
exports.expandPlaceholders = expandPlaceholders;
exports.availableInitTemplates = availableInitTemplates;
exports.availableInitLanguages = availableInitLanguages;
exports.printAvailableTemplates = printAvailableTemplates;
exports.currentlyRecommendedAwsCdkLibFlags = currentlyRecommendedAwsCdkLibFlags;
const childProcess = require("child_process");
const path = require("path");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const fs = require("fs-extra");
const init_hooks_1 = require("./init-hooks");
const root_dir_1 = require("../../cli/root-dir");
const version_1 = require("../../cli/version");
const logging_1 = require("../../logging");
const util_1 = require("../../util");
/* eslint-disable @typescript-eslint/no-var-requires */ // Packages don't have @types module
// eslint-disable-next-line @typescript-eslint/no-require-imports
const camelCase = require('camelcase');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const decamelize = require('decamelize');
/**
 * Initialize a CDK package in the current directory
 */
async function cliInit(options) {
    const canUseNetwork = options.canUseNetwork ?? true;
    const generateOnly = options.generateOnly ?? false;
    const workDir = options.workDir ?? process.cwd();
    if (!options.type && !options.language) {
        await printAvailableTemplates();
        return;
    }
    const type = options.type || 'default'; // "default" is the default type (and maps to "app")
    const template = (await availableInitTemplates()).find((t) => t.hasName(type));
    if (!template) {
        await printAvailableTemplates(options.language);
        throw new toolkit_lib_1.ToolkitError(`Unknown init template: ${type}`);
    }
    if (!options.language && template.languages.length === 1) {
        const language = template.languages[0];
        (0, logging_1.warning)(`No --language was provided, but '${type}' supports only '${language}', so defaulting to --language=${language}`);
    }
    if (!options.language) {
        (0, logging_1.info)(`Available languages for ${chalk.green(type)}: ${template.languages.map((l) => chalk.blue(l)).join(', ')}`);
        throw new toolkit_lib_1.ToolkitError('No language was selected');
    }
    await initializeProject(template, options.language, canUseNetwork, generateOnly, workDir, options.stackName, options.migrate, options.libVersion);
}
/**
 * Returns the name of the Python executable for this OS
 */
function pythonExecutable() {
    let python = 'python3';
    if (process.platform === 'win32') {
        python = 'python';
    }
    return python;
}
const INFO_DOT_JSON = 'info.json';
class InitTemplate {
    static async fromName(templatesDir, name) {
        const basePath = path.join(templatesDir, name);
        const languages = await listDirectory(basePath);
        const initInfo = await fs.readJson(path.join(basePath, INFO_DOT_JSON));
        return new InitTemplate(basePath, name, languages, initInfo);
    }
    constructor(basePath, name, languages, initInfo) {
        this.basePath = basePath;
        this.name = name;
        this.languages = languages;
        this.aliases = new Set();
        this.description = initInfo.description;
        for (const alias of initInfo.aliases || []) {
            this.aliases.add(alias);
        }
    }
    /**
     * @param name - the name that is being checked
     * @returns ``true`` if ``name`` is the name of this template or an alias of it.
     */
    hasName(name) {
        return name === this.name || this.aliases.has(name);
    }
    /**
     * Creates a new instance of this ``InitTemplate`` for a given language to a specified folder.
     *
     * @param language    - the language to instantiate this template with
     * @param targetDirectory - the directory where the template is to be instantiated into
     */
    async install(language, targetDirectory, stackName, libVersion) {
        if (this.languages.indexOf(language) === -1) {
            (0, logging_1.error)(`The ${chalk.blue(language)} language is not supported for ${chalk.green(this.name)} ` +
                `(it supports: ${this.languages.map((l) => chalk.blue(l)).join(', ')})`);
            throw new toolkit_lib_1.ToolkitError(`Unsupported language: ${language}`);
        }
        const projectInfo = {
            name: decamelize(path.basename(path.resolve(targetDirectory))),
            stackName,
            versions: await loadInitVersions(),
        };
        if (libVersion) {
            projectInfo.versions['aws-cdk-lib'] = libVersion;
        }
        const sourceDirectory = path.join(this.basePath, language);
        await this.installFiles(sourceDirectory, targetDirectory, language, projectInfo);
        await this.applyFutureFlags(targetDirectory);
        await (0, init_hooks_1.invokeBuiltinHooks)({ targetDirectory, language, templateName: this.name }, {
            substitutePlaceholdersIn: async (...fileNames) => {
                for (const fileName of fileNames) {
                    const fullPath = path.join(targetDirectory, fileName);
                    const template = await fs.readFile(fullPath, { encoding: 'utf-8' });
                    await fs.writeFile(fullPath, expandPlaceholders(template, language, projectInfo));
                }
            },
            placeholder: (ph) => expandPlaceholders(`%${ph}%`, language, projectInfo),
        });
    }
    async installFiles(sourceDirectory, targetDirectory, language, project) {
        for (const file of await fs.readdir(sourceDirectory)) {
            const fromFile = path.join(sourceDirectory, file);
            const toFile = path.join(targetDirectory, expandPlaceholders(file, language, project));
            if ((await fs.stat(fromFile)).isDirectory()) {
                await fs.mkdir(toFile);
                await this.installFiles(fromFile, toFile, language, project);
                continue;
            }
            else if (file.match(/^.*\.template\.[^.]+$/)) {
                await this.installProcessed(fromFile, toFile.replace(/\.template(\.[^.]+)$/, '$1'), language, project);
                continue;
            }
            else if (file.match(/^.*\.hook\.(d.)?[^.]+$/)) {
                // Ignore
                continue;
            }
            else {
                await fs.copy(fromFile, toFile);
            }
        }
    }
    async installProcessed(templatePath, toFile, language, project) {
        const template = await fs.readFile(templatePath, { encoding: 'utf-8' });
        await fs.writeFile(toFile, expandPlaceholders(template, language, project));
    }
    /**
     * Adds context variables to `cdk.json` in the generated project directory to
     * enable future behavior for new projects.
     */
    async applyFutureFlags(projectDir) {
        const cdkJson = path.join(projectDir, 'cdk.json');
        if (!(await fs.pathExists(cdkJson))) {
            return;
        }
        const config = await fs.readJson(cdkJson);
        config.context = {
            ...config.context,
            ...await currentlyRecommendedAwsCdkLibFlags(),
        };
        await fs.writeJson(cdkJson, config, { spaces: 2 });
    }
    async addMigrateContext(projectDir) {
        const cdkJson = path.join(projectDir, 'cdk.json');
        if (!(await fs.pathExists(cdkJson))) {
            return;
        }
        const config = await fs.readJson(cdkJson);
        config.context = {
            ...config.context,
            'cdk-migrate': true,
        };
        await fs.writeJson(cdkJson, config, { spaces: 2 });
    }
}
exports.InitTemplate = InitTemplate;
function expandPlaceholders(template, language, project) {
    const cdkVersion = project.versions['aws-cdk-lib'];
    const cdkCliVersion = project.versions['aws-cdk'];
    let constructsVersion = project.versions.constructs;
    switch (language) {
        case 'java':
        case 'csharp':
        case 'fsharp':
            constructsVersion = (0, util_1.rangeFromSemver)(constructsVersion, 'bracket');
            break;
        case 'python':
            constructsVersion = (0, util_1.rangeFromSemver)(constructsVersion, 'pep');
            break;
    }
    return template
        .replace(/%name%/g, project.name)
        .replace(/%stackname%/, project.stackName ?? '%name.PascalCased%Stack')
        .replace(/%PascalNameSpace%/, project.stackName ? camelCase(project.stackName + 'Stack', { pascalCase: true }) : '%name.PascalCased%')
        .replace(/%PascalStackProps%/, project.stackName ? camelCase(project.stackName, { pascalCase: true }) + 'StackProps' : 'StackProps')
        .replace(/%name\.camelCased%/g, camelCase(project.name))
        .replace(/%name\.PascalCased%/g, camelCase(project.name, { pascalCase: true }))
        .replace(/%cdk-version%/g, cdkVersion)
        .replace(/%cdk-cli-version%/g, cdkCliVersion)
        .replace(/%constructs-version%/g, constructsVersion)
        .replace(/%cdk-home%/g, (0, util_1.cdkHomeDir)())
        .replace(/%name\.PythonModule%/g, project.name.replace(/-/g, '_'))
        .replace(/%python-executable%/g, pythonExecutable())
        .replace(/%name\.StackName%/g, project.name.replace(/[^A-Za-z0-9-]/g, '-'));
}
async function availableInitTemplates() {
    return new Promise(async (resolve) => {
        try {
            const templatesDir = path.join((0, root_dir_1.cliRootDir)(), 'lib', 'init-templates');
            const templateNames = await listDirectory(templatesDir);
            const templates = new Array();
            for (const templateName of templateNames) {
                templates.push(await InitTemplate.fromName(templatesDir, templateName));
            }
            resolve(templates);
        }
        catch {
            resolve([]);
        }
    });
}
async function availableInitLanguages() {
    return new Promise(async (resolve) => {
        const templates = await availableInitTemplates();
        const result = new Set();
        for (const template of templates) {
            for (const language of template.languages) {
                result.add(language);
            }
        }
        resolve([...result]);
    });
}
/**
 * @param dirPath - is the directory to be listed.
 * @returns the list of file or directory names contained in ``dirPath``, excluding any dot-file, and sorted.
 */
async function listDirectory(dirPath) {
    return ((await fs.readdir(dirPath))
        .filter((p) => !p.startsWith('.'))
        .filter((p) => !(p === 'LICENSE'))
        // if, for some reason, the temp folder for the hook doesn't get deleted we don't want to display it in this list
        .filter((p) => !(p === INFO_DOT_JSON))
        .sort());
}
async function printAvailableTemplates(language) {
    (0, logging_1.info)('Available templates:');
    for (const template of await availableInitTemplates()) {
        if (language && template.languages.indexOf(language) === -1) {
            continue;
        }
        (0, logging_1.info)(`* ${chalk.green(template.name)}: ${template.description}`);
        const languageArg = language
            ? chalk.bold(language)
            : template.languages.length > 1
                ? `[${template.languages.map((t) => chalk.bold(t)).join('|')}]`
                : chalk.bold(template.languages[0]);
        (0, logging_1.info)(`   └─ ${chalk.blue(`cdk init ${chalk.bold(template.name)} --language=${languageArg}`)}`);
    }
}
async function initializeProject(template, language, canUseNetwork, generateOnly, workDir, stackName, migrate, cdkVersion) {
    await assertIsEmptyDirectory(workDir);
    (0, logging_1.info)(`Applying project template ${chalk.green(template.name)} for ${chalk.blue(language)}`);
    await template.install(language, workDir, stackName, cdkVersion);
    if (migrate) {
        await template.addMigrateContext(workDir);
    }
    if (await fs.pathExists(`${workDir}/README.md`)) {
        const readme = await fs.readFile(`${workDir}/README.md`, { encoding: 'utf-8' });
        (0, logging_1.info)(chalk.green(readme));
    }
    if (!generateOnly) {
        await initializeGitRepository(workDir);
        await postInstall(language, canUseNetwork, workDir);
    }
    (0, logging_1.info)('✅ All done!');
}
async function assertIsEmptyDirectory(workDir) {
    const files = await fs.readdir(workDir);
    if (files.filter((f) => !f.startsWith('.')).length !== 0) {
        throw new toolkit_lib_1.ToolkitError('`cdk init` cannot be run in a non-empty directory!');
    }
}
async function initializeGitRepository(workDir) {
    if (await isInGitRepository(workDir)) {
        return;
    }
    (0, logging_1.info)('Initializing a new git repository...');
    try {
        await execute('git', ['init'], { cwd: workDir });
        await execute('git', ['add', '.'], { cwd: workDir });
        await execute('git', ['commit', '--message="Initial commit"', '--no-gpg-sign'], { cwd: workDir });
    }
    catch {
        (0, logging_1.warning)('Unable to initialize git repository for your project.');
    }
}
async function postInstall(language, canUseNetwork, workDir) {
    switch (language) {
        case 'javascript':
            return postInstallJavascript(canUseNetwork, workDir);
        case 'typescript':
            return postInstallTypescript(canUseNetwork, workDir);
        case 'java':
            return postInstallJava(canUseNetwork, workDir);
        case 'python':
            return postInstallPython(workDir);
    }
}
async function postInstallJavascript(canUseNetwork, cwd) {
    return postInstallTypescript(canUseNetwork, cwd);
}
async function postInstallTypescript(canUseNetwork, cwd) {
    const command = 'npm';
    if (!canUseNetwork) {
        (0, logging_1.warning)(`Please run '${command} install'!`);
        return;
    }
    (0, logging_1.info)(`Executing ${chalk.green(`${command} install`)}...`);
    try {
        await execute(command, ['install'], { cwd });
    }
    catch (e) {
        (0, logging_1.warning)(`${command} install failed: ` + (0, util_1.formatErrorMessage)(e));
    }
}
async function postInstallJava(canUseNetwork, cwd) {
    const mvnPackageWarning = "Please run 'mvn package'!";
    if (!canUseNetwork) {
        (0, logging_1.warning)(mvnPackageWarning);
        return;
    }
    (0, logging_1.info)("Executing 'mvn package'");
    try {
        await execute('mvn', ['package'], { cwd });
    }
    catch {
        (0, logging_1.warning)('Unable to package compiled code as JAR');
        (0, logging_1.warning)(mvnPackageWarning);
    }
}
async function postInstallPython(cwd) {
    const python = pythonExecutable();
    (0, logging_1.warning)(`Please run '${python} -m venv .venv'!`);
    (0, logging_1.info)(`Executing ${chalk.green('Creating virtualenv...')}`);
    try {
        await execute(python, ['-m venv', '.venv'], { cwd });
    }
    catch {
        (0, logging_1.warning)('Unable to create virtualenv automatically');
        (0, logging_1.warning)(`Please run '${python} -m venv .venv'!`);
    }
}
/**
 * @param dir - a directory to be checked
 * @returns true if ``dir`` is within a git repository.
 */
async function isInGitRepository(dir) {
    while (true) {
        if (await fs.pathExists(path.join(dir, '.git'))) {
            return true;
        }
        if (isRoot(dir)) {
            return false;
        }
        dir = path.dirname(dir);
    }
}
/**
 * @param dir - a directory to be checked.
 * @returns true if ``dir`` is the root of a filesystem.
 */
function isRoot(dir) {
    return path.dirname(dir) === dir;
}
/**
 * Executes `command`. STDERR is emitted in real-time.
 *
 * If command exits with non-zero exit code, an exceprion is thrown and includes
 * the contents of STDOUT.
 *
 * @returns STDOUT (if successful).
 */
async function execute(cmd, args, { cwd }) {
    const child = childProcess.spawn(cmd, args, {
        cwd,
        shell: true,
        stdio: ['ignore', 'pipe', 'inherit'],
    });
    let stdout = '';
    child.stdout.on('data', (chunk) => (stdout += chunk.toString()));
    return new Promise((ok, fail) => {
        child.once('error', (err) => fail(err));
        child.once('exit', (status) => {
            if (status === 0) {
                return ok(stdout);
            }
            else {
                (0, logging_1.error)(stdout);
                return fail(new toolkit_lib_1.ToolkitError(`${cmd} exited with status ${status}`));
            }
        });
    });
}
/**
 * Return the 'aws-cdk-lib' version we will init
 *
 * This has been built into the CLI at build time.
 */
async function loadInitVersions() {
    const initVersionFile = path.join((0, root_dir_1.cliRootDir)(), 'lib', 'init-templates', '.init-version.json');
    const contents = JSON.parse(await fs.readFile(initVersionFile, { encoding: 'utf-8' }));
    const ret = {
        'aws-cdk-lib': contents['aws-cdk-lib'],
        'constructs': contents.constructs,
        'aws-cdk': (0, version_1.versionNumber)(),
    };
    for (const [key, value] of Object.entries(ret)) {
        /* c8 ignore start */
        if (!value) {
            throw new toolkit_lib_1.ToolkitError(`Missing init version from ${initVersionFile}: ${key}`);
        }
        /* c8 ignore stop */
    }
    return ret;
}
/**
 * Return the currently recommended flags for `aws-cdk-lib`.
 *
 * These have been built into the CLI at build time.
 */
async function currentlyRecommendedAwsCdkLibFlags() {
    const recommendedFlagsFile = path.join((0, root_dir_1.cliRootDir)(), 'lib', 'init-templates', '.recommended-feature-flags.json');
    return JSON.parse(await fs.readFile(recommendedFlagsFile, { encoding: 'utf-8' }));
}
//# sourceMappingURL=data:application/json;base64,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