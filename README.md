# CloudFront Terraform to CDK Migration Project

This project provides a safe migration path from Terraform-managed CloudFront distributions to AWS CDK, using the AWS Labs CDK MCP server for enhanced capabilities.

## 🎯 Project Goal

**Import and document existing CloudFront distributions without creating new resources**, enabling a safe migration from Terraform to CDK.

## 📋 Current State

### Existing CloudFront Distributions (Terraform-managed)
- **E1Y2GZXHXCGI8H**: API Gateway with custom headers forwarding
- **E131RZ69QYLX7O**: API Gateway with cache behaviors for JPG files
- **EXZC66UKJAABK**: External website distribution (quotewizard.com)
- **E2EZONTUD8DA1O**: API Gateway with device detection headers
- **ES27SJ7X3UX1M**: Complex multi-origin with custom domain (test-publisher.delty.com)
- **E10NZFW9D6AMU2**: Multi-origin distribution
- **E1YNWVHTAGY30T**: S3 website with WAF protection

### Associated Resources
- **ACM Certificate**: `arn:aws:acm:us-east-1:************:certificate/1396c717-ff76-403a-9ab1-cf2185a83196`
- **S3 Bucket**: `deleteonly-dev`
- **WAF Web ACL**: CloudFront-managed WAF for S3 website

## 🏗️ Project Structure

```
├── bin/awsmcpdemo.ts                           # CDK app entry point
├── lib/cloudfront-import-stack.ts              # Import existing distributions
├── docs/existing-cloudfront-configuration.md   # Detailed current state
├── TERRAFORM_TO_CDK_MIGRATION.md              # Migration guide
├── .vscode/mcp.json                            # AWS Labs CDK MCP configuration
└── package.json                               # Dependencies
```

## 🔧 AWS Labs CDK MCP Integration

This project leverages the AWS Labs CDK MCP server for enhanced CDK capabilities:

```json
{
  "servers": {
    "awslabs.cdk-mcp-server": {
      "command": "uvx",
      "args": ["awslabs.cdk-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR"
      }
    }
  }
}
```

## 🚀 Quick Start

### Prerequisites
- AWS CLI configured with SSO profile: `AWS-dev-Delty-Admin-************`
- Node.js and npm installed
- AWS CDK CLI installed
- VS Code with MCP support (optional but recommended)

### Setup
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Synthesize CloudFormation (verify no new resources)
npx cdk synth --profile AWS-dev-Delty-Admin-************
```

### Deploy Import Stack
```bash
# Deploy import stack (creates no new CloudFront resources)
npx cdk deploy CloudFrontImportStack --profile AWS-dev-Delty-Admin-************
```

## ✅ What This Does

### ✅ Safe Operations
- **Imports existing distributions** using `Distribution.fromDistributionAttributes()`
- **References existing certificate and S3 bucket** without modification
- **Creates SSM parameters** to track migration status
- **Generates outputs** with all distribution IDs for reference
- **Documents complete configuration** for migration planning

### ❌ What This Does NOT Do
- **Create new CloudFront distributions**
- **Modify existing distributions**
- **Change DNS or routing**
- **Affect Terraform state**
- **Interrupt service**

## 📊 Verification

After deployment, verify the import was successful:

```bash
# Check stack outputs
aws cloudformation describe-stacks \
  --stack-name CloudFrontImportStack \
  --profile AWS-dev-Delty-Admin-************ \
  --query 'Stacks[0].Outputs'

# Verify SSM parameters
aws ssm get-parameter \
  --name "/cloudfront/terraform-migration/status" \
  --profile AWS-dev-Delty-Admin-************
```

## 📚 Documentation

- **[Existing Configuration](docs/existing-cloudfront-configuration.md)**: Complete current state documentation
- **[Migration Guide](TERRAFORM_TO_CDK_MIGRATION.md)**: Step-by-step migration process
- **[AWS Labs CDK MCP](https://github.com/awslabs/cdk-mcp-server)**: Enhanced CDK capabilities

## 🔄 Migration Strategy

### Phase 1: Import (Current) ✅
- Import all existing distributions
- Document current configuration
- Establish CDK references

### Phase 2: Validation
- Deploy import stack
- Verify all references work
- Test CDK synthesis

### Phase 3: Gradual Migration
- Start with simplest distributions
- Create new CDK-managed distributions
- Test thoroughly before switching

### Phase 4: Cleanup
- Remove Terraform state
- Update CI/CD pipelines
- Complete migration

## 🛡️ Safety Features

1. **No Resource Creation**: All distributions imported, not created
2. **Terraform State Preserved**: Original state remains intact
3. **Rollback Capability**: Can revert to Terraform management
4. **Complete Documentation**: All configurations captured
5. **Gradual Migration**: Move distributions one at a time

## 🔍 Outputs

The stack provides these outputs for reference:

- Distribution IDs for all 7 CloudFront distributions
- ACM certificate ARN
- S3 bucket name
- Migration status tracking

## 🆘 Troubleshooting

### Common Issues
- **Distribution not found**: Verify distribution ID and region
- **Certificate import fails**: Check ARN and ensure it's in us-east-1
- **Profile issues**: Refresh AWS SSO login

### MCP Server Issues
```bash
# Check MCP server
uvx awslabs.cdk-mcp-server@latest --help

# Restart VS Code if needed
```

## 🎯 Next Steps

1. **Deploy the import stack** to establish CDK references
2. **Review the migration guide** for detailed next steps
3. **Plan gradual migration** starting with simple distributions
4. **Test new CDK-managed distributions** before switching traffic

## Useful commands

* `npm run build`   compile typescript to js
* `npm run watch`   watch for changes and compile
* `npm run test`    perform the jest unit tests
* `npx cdk deploy`  deploy this stack to your default AWS account/region
* `npx cdk diff`    compare deployed stack with current state
* `npx cdk synth`   emits the synthesized CloudFormation template

---

**⚠️ Important**: This project imports existing resources without creating new ones. Your current CloudFront distributions will continue to work exactly as they do now, managed by Terraform, until you explicitly migrate them to CDK management.
