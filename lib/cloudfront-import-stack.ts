import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as certificatemanager from 'aws-cdk-lib/aws-certificatemanager';

/**
 * Stack that imports existing CloudFront distributions from Terraform-managed infrastructure
 * This represents the current state without creating new resources - for migration purposes
 */
export class CloudFrontImportStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Import existing CloudFront distributions to reflect current Terraform state
    this.importExistingDistributions();
    this.createConfigurationDocumentation();
  }

  private importExistingDistributions() {
    // Import existing CloudFront distributions by their IDs
    // These represent your current Terraform-managed infrastructure
    
    // Distribution E1Y2GZXHXCGI8H - API Gateway with custom headers forwarding
    const apiGatewayDist1 = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingApiGatewayDist1', 
      {
        distributionId: 'E1Y2GZXHXCGI8H',
        domainName: 'd2s7hrgrz2s1ba.cloudfront.net',
      }
    );

    // Distribution E131RZ69QYLX7O - API Gateway with cache behaviors
    const apiGatewayDist2 = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingApiGatewayDist2', 
      {
        distributionId: 'E131RZ69QYLX7O',
        domainName: 'dtor21q5y56di.cloudfront.net',
      }
    );

    // Distribution EXZC66UKJAABK - External website distribution
    const externalWebsiteDist = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingExternalWebsiteDist', 
      {
        distributionId: 'EXZC66UKJAABK',
        domainName: 'd1q1i6afcjz18f.cloudfront.net',
      }
    );

    // Distribution E2EZONTUD8DA1O - API Gateway with device detection headers
    const apiGatewayDist3 = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingApiGatewayDist3', 
      {
        distributionId: 'E2EZONTUD8DA1O',
        domainName: 'd6omm258hzaqm.cloudfront.net',
      }
    );

    // Distribution ES27SJ7X3UX1M - Complex multi-origin distribution with custom domain
    const complexDist = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingComplexDist', 
      {
        distributionId: 'ES27SJ7X3UX1M',
        domainName: 'd121gt36t1o2vg.cloudfront.net',
      }
    );

    // Distribution E10NZFW9D6AMU2 - Multi-origin distribution
    const multiOriginDist = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingMultiOriginDist', 
      {
        distributionId: 'E10NZFW9D6AMU2',
        domainName: 'd1vdttacwqw93n.cloudfront.net',
      }
    );

    // Distribution E1YNWVHTAGY30T - S3 Website with WAF
    const s3WebsiteDist = cloudfront.Distribution.fromDistributionAttributes(
      this, 
      'ExistingS3WebsiteDist', 
      {
        distributionId: 'E1YNWVHTAGY30T',
        domainName: 'd3tqauws6s7t6o.cloudfront.net',
      }
    );

    // Import existing ACM certificate
    const existingCertificate = certificatemanager.Certificate.fromCertificateArn(
      this,
      'ExistingDeltyTestCertificate',
      'arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196'
    );

    // Import existing S3 bucket
    const existingS3Bucket = s3.Bucket.fromBucketName(
      this, 
      'ExistingDeleteOnlyDevBucket', 
      'deleteonly-dev'
    );

    // Store references for potential future use
    this.addOutputs(
      apiGatewayDist1,
      apiGatewayDist2,
      apiGatewayDist3,
      externalWebsiteDist,
      complexDist,
      multiOriginDist,
      s3WebsiteDist,
      existingCertificate,
      existingS3Bucket
    );
  }

  private addOutputs(
    apiGatewayDist1: cloudfront.IDistribution,
    apiGatewayDist2: cloudfront.IDistribution,
    apiGatewayDist3: cloudfront.IDistribution,
    externalWebsiteDist: cloudfront.IDistribution,
    complexDist: cloudfront.IDistribution,
    multiOriginDist: cloudfront.IDistribution,
    s3WebsiteDist: cloudfront.IDistribution,
    certificate: certificatemanager.ICertificate,
    s3Bucket: s3.IBucket
  ) {
    // Output existing distribution information for reference
    new cdk.CfnOutput(this, 'ExistingApiGatewayDist1Id', {
      value: apiGatewayDist1.distributionId,
      description: 'Existing API Gateway Distribution 1 ID (E1Y2GZXHXCGI8H)',
    });

    new cdk.CfnOutput(this, 'ExistingApiGatewayDist2Id', {
      value: apiGatewayDist2.distributionId,
      description: 'Existing API Gateway Distribution 2 ID (E131RZ69QYLX7O)',
    });

    new cdk.CfnOutput(this, 'ExistingApiGatewayDist3Id', {
      value: apiGatewayDist3.distributionId,
      description: 'Existing API Gateway Distribution 3 ID (E2EZONTUD8DA1O)',
    });

    new cdk.CfnOutput(this, 'ExistingExternalWebsiteDistId', {
      value: externalWebsiteDist.distributionId,
      description: 'Existing External Website Distribution ID (EXZC66UKJAABK)',
    });

    new cdk.CfnOutput(this, 'ExistingComplexDistId', {
      value: complexDist.distributionId,
      description: 'Existing Complex Distribution ID (ES27SJ7X3UX1M)',
    });

    new cdk.CfnOutput(this, 'ExistingMultiOriginDistId', {
      value: multiOriginDist.distributionId,
      description: 'Existing Multi-Origin Distribution ID (E10NZFW9D6AMU2)',
    });

    new cdk.CfnOutput(this, 'ExistingS3WebsiteDistId', {
      value: s3WebsiteDist.distributionId,
      description: 'Existing S3 Website Distribution ID (E1YNWVHTAGY30T)',
    });

    new cdk.CfnOutput(this, 'ExistingCertificateArn', {
      value: certificate.certificateArn,
      description: 'Existing ACM Certificate ARN',
    });

    new cdk.CfnOutput(this, 'ExistingS3BucketName', {
      value: s3Bucket.bucketName,
      description: 'Existing S3 Bucket Name',
    });
  }

  private createConfigurationDocumentation() {
    // Create SSM parameters to document the current configuration
    // This helps with the Terraform to CDK migration
    
    new cdk.aws_ssm.StringParameter(this, 'TerraformMigrationStatus', {
      parameterName: '/cloudfront/terraform-migration/status',
      stringValue: 'imported-existing-resources',
      description: 'Status of Terraform to CDK migration for CloudFront distributions',
    });

    new cdk.aws_ssm.StringParameter(this, 'DistributionInventory', {
      parameterName: '/cloudfront/terraform-migration/distribution-count',
      stringValue: '7',
      description: 'Number of CloudFront distributions imported from Terraform',
    });
  }
}
