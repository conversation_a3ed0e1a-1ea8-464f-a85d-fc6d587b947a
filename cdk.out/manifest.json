{"version": "44.0.0", "artifacts": {"AwsmcpdemoStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "AwsmcpdemoStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "AwsmcpdemoStack": {"type": "aws:cloudformation:stack", "environment": "aws://902537659456/us-east-1", "properties": {"templateFile": "AwsmcpdemoStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-deploy-role-902537659456-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-cfn-exec-role-902537659456-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-902537659456-us-east-1/9771fa8a13ce3438d312d8253090c292b8a033159aa82e362b19b789f999cf76.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["AwsmcpdemoStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-lookup-role-902537659456-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["AwsmcpdemoStack.assets"], "metadata": {"/AwsmcpdemoStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/AwsmcpdemoStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/AwsmcpdemoStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "AwsmcpdemoStack"}, "ApiGatewayDistributionStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "ApiGatewayDistributionStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "ApiGatewayDistributionStack": {"type": "aws:cloudformation:stack", "environment": "aws://902537659456/us-east-1", "properties": {"templateFile": "ApiGatewayDistributionStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-deploy-role-902537659456-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-cfn-exec-role-902537659456-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-902537659456-us-east-1/81c75b2b2e20fb2ffe4bdfb039d6f2b930b2495c6fc6ed9f71023448f1e4e134.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["ApiGatewayDistributionStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-lookup-role-902537659456-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["ApiGatewayDistributionStack.assets"], "metadata": {"/ApiGatewayDistributionStack/ApiGatewayCache1": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ApiGatewayDistributionStack/ApiGatewayCache1/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayCache11DB2D25A"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution1": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http1.1", "priceClass": "PriceClass_All", "enableIpv6": false, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}}], "/ApiGatewayDistributionStack/ApiGatewayDistribution1/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution1E054F055"}], "/ApiGatewayDistributionStack/ApiGatewayCache2": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ApiGatewayDistributionStack/ApiGatewayCache2/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayCache2A091AD93"}], "/ApiGatewayDistributionStack/JpgCachePolicy": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ApiGatewayDistributionStack/JpgCachePolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "JpgCachePolicy8CC17EDB"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution2": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http1.1", "priceClass": "PriceClass_All", "enableIpv6": false, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "additionalBehaviors": "*", "errorResponses": [{"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}], "certificate": "*", "domainNames": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}}], "/ApiGatewayDistributionStack/ApiGatewayDistribution2/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution2D19290A1"}], "/ApiGatewayDistributionStack/ApiGatewayCache3": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ApiGatewayDistributionStack/ApiGatewayCache3/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayCache31542FF1E"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution3": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}}], "/ApiGatewayDistributionStack/ApiGatewayDistribution3/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution3376E283A"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution1DomainName": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution1DomainName"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution2DomainName": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution2DomainName"}], "/ApiGatewayDistributionStack/ApiGatewayDistribution3DomainName": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayDistribution3DomainName"}], "/ApiGatewayDistributionStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/ApiGatewayDistributionStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/ApiGatewayDistributionStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "ApiGatewayDistributionStack"}, "S3WebsiteDistributionStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "S3WebsiteDistributionStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "S3WebsiteDistributionStack": {"type": "aws:cloudformation:stack", "environment": "aws://902537659456/us-east-1", "properties": {"templateFile": "S3WebsiteDistributionStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-deploy-role-902537659456-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-cfn-exec-role-902537659456-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-902537659456-us-east-1/8897392e9dd84b927eb9341733531f81ab17fd903ea5584b81ea1ee3185b4263.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["S3WebsiteDistributionStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-lookup-role-902537659456-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["S3WebsiteDistributionStack.assets"], "metadata": {"/S3WebsiteDistributionStack/CloudFrontWebACL": [{"type": "aws:cdk:logicalId", "data": "CloudFrontWebACL"}], "/S3WebsiteDistributionStack/S3WebsiteDistribution": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "webAclId": "*", "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "https-only", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}}], "/S3WebsiteDistributionStack/S3WebsiteDistribution/Resource": [{"type": "aws:cdk:logicalId", "data": "S3WebsiteDistributionEFFC0A69"}], "/S3WebsiteDistributionStack/ExternalWebsiteCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/S3WebsiteDistributionStack/ExternalWebsiteCache/Resource": [{"type": "aws:cdk:logicalId", "data": "ExternalWebsiteCache81929B10"}], "/S3WebsiteDistributionStack/ExternalWebsiteDistribution": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "allow-all", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}}], "/S3WebsiteDistributionStack/ExternalWebsiteDistribution/Resource": [{"type": "aws:cdk:logicalId", "data": "ExternalWebsiteDistribution39A9CF4A"}], "/S3WebsiteDistributionStack/MultiOriginCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/S3WebsiteDistributionStack/MultiOriginCache/Resource": [{"type": "aws:cdk:logicalId", "data": "MultiOriginCache2868F312"}], "/S3WebsiteDistributionStack/MultiOriginDistribution": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}, "certificate": "*", "domainNames": "*"}}], "/S3WebsiteDistributionStack/MultiOriginDistribution/Resource": [{"type": "aws:cdk:logicalId", "data": "MultiOriginDistribution39008539"}], "/S3WebsiteDistributionStack/S3WebsiteDistributionDomainName": [{"type": "aws:cdk:logicalId", "data": "S3WebsiteDistributionDomainName"}], "/S3WebsiteDistributionStack/ExternalWebsiteDistributionDomainName": [{"type": "aws:cdk:logicalId", "data": "ExternalWebsiteDistributionDomainName"}], "/S3WebsiteDistributionStack/MultiOriginDistributionDomainName": [{"type": "aws:cdk:logicalId", "data": "MultiOriginDistributionDomainName"}], "/S3WebsiteDistributionStack/WebACLArn": [{"type": "aws:cdk:logicalId", "data": "WebACLArn"}], "/S3WebsiteDistributionStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/S3WebsiteDistributionStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/S3WebsiteDistributionStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "S3WebsiteDistributionStack"}, "ComplexDistributionStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "ComplexDistributionStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "ComplexDistributionStack": {"type": "aws:cloudformation:stack", "environment": "aws://902537659456/us-east-1", "properties": {"templateFile": "ComplexDistributionStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-deploy-role-902537659456-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-cfn-exec-role-902537659456-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-902537659456-us-east-1/29e6f0848579788766b32aff3bcf354a0cffaf7e3220b9ca0dd67bc8eff26831.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["ComplexDistributionStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-lookup-role-902537659456-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["ComplexDistributionStack.assets"], "metadata": {"/ComplexDistributionStack/PublisherPortalCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ComplexDistributionStack/PublisherPortalCache/Resource": [{"type": "aws:cdk:logicalId", "data": "PublisherPortalCache8BEB5AAB"}], "/ComplexDistributionStack/SftpHandlerCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ComplexDistributionStack/SftpHandlerCache/Resource": [{"type": "aws:cdk:logicalId", "data": "SftpHandlerCacheD292D7B8"}], "/ComplexDistributionStack/BulkEditCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ComplexDistributionStack/BulkEditCache/Resource": [{"type": "aws:cdk:logicalId", "data": "BulkEditCacheFC5EA5C2"}], "/ComplexDistributionStack/KubeCache": [{"type": "aws:cdk:analytics:construct", "data": {"cachePolicyName": "*", "comment": "*", "defaultTtl": "*", "maxTtl": "*", "minTtl": "*", "headerBehavior": "*", "queryStringBehavior": "*", "cookieBehavior": "*"}}], "/ComplexDistributionStack/KubeCache/Resource": [{"type": "aws:cdk:logicalId", "data": "KubeCacheAC2B3D63"}], "/ComplexDistributionStack/ComplexDistribution": [{"type": "aws:cdk:analytics:construct", "data": {"comment": "*", "enabled": true, "httpVersion": "http2", "priceClass": "PriceClass_All", "enableIpv6": true, "certificate": "*", "domainNames": "*", "defaultBehavior": {"origin": "*", "viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}, "additionalBehaviors": "*", "errorResponses": [{"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}, {"httpStatus": "*", "ttl": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": true, "cachePolicy": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addBehavior": ["*", {}, {"viewerProtocolPolicy": "redirect-to-https", "allowedMethods": "*", "cachedMethods": "*", "compress": false, "cachePolicy": "*"}]}}], "/ComplexDistributionStack/ComplexDistribution/Origin2/S3Origin": [{"type": "aws:cdk:analytics:construct", "data": {}}], "/ComplexDistributionStack/ComplexDistribution/Origin2/S3Origin/Resource": [{"type": "aws:cdk:logicalId", "data": "ComplexDistributionOrigin2S3Origin5D9E4DD2"}], "/ComplexDistributionStack/ComplexDistribution/Resource": [{"type": "aws:cdk:logicalId", "data": "ComplexDistribution89A8BF2B"}], "/ComplexDistributionStack/ComplexDistributionDomainName": [{"type": "aws:cdk:logicalId", "data": "ComplexDistributionDomainName"}], "/ComplexDistributionStack/ComplexDistributionId": [{"type": "aws:cdk:logicalId", "data": "ComplexDistributionId"}], "/ComplexDistributionStack/CustomDomainName": [{"type": "aws:cdk:logicalId", "data": "CustomDomainName"}], "/ComplexDistributionStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/ComplexDistributionStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/ComplexDistributionStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "ComplexDistributionStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1018.1"}