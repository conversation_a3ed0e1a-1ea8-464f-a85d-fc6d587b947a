"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudFrontImportStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront = __importStar(require("aws-cdk-lib/aws-cloudfront"));
const s3 = __importStar(require("aws-cdk-lib/aws-s3"));
const certificatemanager = __importStar(require("aws-cdk-lib/aws-certificatemanager"));
/**
 * Stack that imports existing CloudFront distributions from Terraform-managed infrastructure
 * This represents the current state without creating new resources - for migration purposes
 */
class CloudFrontImportStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Import existing CloudFront distributions to reflect current Terraform state
        this.importExistingDistributions();
        this.createConfigurationDocumentation();
    }
    importExistingDistributions() {
        // Import existing CloudFront distributions by their IDs
        // These represent your current Terraform-managed infrastructure
        // Distribution E1Y2GZXHXCGI8H - API Gateway with custom headers forwarding
        const apiGatewayDist1 = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingApiGatewayDist1', {
            distributionId: 'E1Y2GZXHXCGI8H',
            domainName: 'd2s7hrgrz2s1ba.cloudfront.net',
        });
        // Distribution E131RZ69QYLX7O - API Gateway with cache behaviors
        const apiGatewayDist2 = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingApiGatewayDist2', {
            distributionId: 'E131RZ69QYLX7O',
            domainName: 'dtor21q5y56di.cloudfront.net',
        });
        // Distribution EXZC66UKJAABK - External website distribution
        const externalWebsiteDist = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingExternalWebsiteDist', {
            distributionId: 'EXZC66UKJAABK',
            domainName: 'd1q1i6afcjz18f.cloudfront.net',
        });
        // Distribution E2EZONTUD8DA1O - API Gateway with device detection headers
        const apiGatewayDist3 = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingApiGatewayDist3', {
            distributionId: 'E2EZONTUD8DA1O',
            domainName: 'd6omm258hzaqm.cloudfront.net',
        });
        // Distribution ES27SJ7X3UX1M - Complex multi-origin distribution with custom domain
        const complexDist = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingComplexDist', {
            distributionId: 'ES27SJ7X3UX1M',
            domainName: 'd121gt36t1o2vg.cloudfront.net',
        });
        // Distribution E10NZFW9D6AMU2 - Multi-origin distribution
        const multiOriginDist = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingMultiOriginDist', {
            distributionId: 'E10NZFW9D6AMU2',
            domainName: 'd1vdttacwqw93n.cloudfront.net',
        });
        // Distribution E1YNWVHTAGY30T - S3 Website with WAF
        const s3WebsiteDist = cloudfront.Distribution.fromDistributionAttributes(this, 'ExistingS3WebsiteDist', {
            distributionId: 'E1YNWVHTAGY30T',
            domainName: 'd3tqauws6s7t6o.cloudfront.net',
        });
        // Import existing ACM certificate
        const existingCertificate = certificatemanager.Certificate.fromCertificateArn(this, 'ExistingDeltyTestCertificate', 'arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196');
        // Import existing S3 bucket
        const existingS3Bucket = s3.Bucket.fromBucketName(this, 'ExistingDeleteOnlyDevBucket', 'deleteonly-dev');
        // Store references for potential future use
        this.addOutputs(apiGatewayDist1, apiGatewayDist2, apiGatewayDist3, externalWebsiteDist, complexDist, multiOriginDist, s3WebsiteDist, existingCertificate, existingS3Bucket);
    }
    addOutputs(apiGatewayDist1, apiGatewayDist2, apiGatewayDist3, externalWebsiteDist, complexDist, multiOriginDist, s3WebsiteDist, certificate, s3Bucket) {
        // Output existing distribution information for reference
        new cdk.CfnOutput(this, 'ExistingApiGatewayDist1Id', {
            value: apiGatewayDist1.distributionId,
            description: 'Existing API Gateway Distribution 1 ID (E1Y2GZXHXCGI8H)',
        });
        new cdk.CfnOutput(this, 'ExistingApiGatewayDist2Id', {
            value: apiGatewayDist2.distributionId,
            description: 'Existing API Gateway Distribution 2 ID (E131RZ69QYLX7O)',
        });
        new cdk.CfnOutput(this, 'ExistingApiGatewayDist3Id', {
            value: apiGatewayDist3.distributionId,
            description: 'Existing API Gateway Distribution 3 ID (E2EZONTUD8DA1O)',
        });
        new cdk.CfnOutput(this, 'ExistingExternalWebsiteDistId', {
            value: externalWebsiteDist.distributionId,
            description: 'Existing External Website Distribution ID (EXZC66UKJAABK)',
        });
        new cdk.CfnOutput(this, 'ExistingComplexDistId', {
            value: complexDist.distributionId,
            description: 'Existing Complex Distribution ID (ES27SJ7X3UX1M)',
        });
        new cdk.CfnOutput(this, 'ExistingMultiOriginDistId', {
            value: multiOriginDist.distributionId,
            description: 'Existing Multi-Origin Distribution ID (E10NZFW9D6AMU2)',
        });
        new cdk.CfnOutput(this, 'ExistingS3WebsiteDistId', {
            value: s3WebsiteDist.distributionId,
            description: 'Existing S3 Website Distribution ID (E1YNWVHTAGY30T)',
        });
        new cdk.CfnOutput(this, 'ExistingCertificateArn', {
            value: certificate.certificateArn,
            description: 'Existing ACM Certificate ARN',
        });
        new cdk.CfnOutput(this, 'ExistingS3BucketName', {
            value: s3Bucket.bucketName,
            description: 'Existing S3 Bucket Name',
        });
    }
    createConfigurationDocumentation() {
        // Create SSM parameters to document the current configuration
        // This helps with the Terraform to CDK migration
        new cdk.aws_ssm.StringParameter(this, 'TerraformMigrationStatus', {
            parameterName: '/cloudfront/terraform-migration/status',
            stringValue: 'imported-existing-resources',
            description: 'Status of Terraform to CDK migration for CloudFront distributions',
        });
        new cdk.aws_ssm.StringParameter(this, 'DistributionInventory', {
            parameterName: '/cloudfront/terraform-migration/distribution-count',
            stringValue: '7',
            description: 'Number of CloudFront distributions imported from Terraform',
        });
    }
}
exports.CloudFrontImportStack = CloudFrontImportStack;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2xvdWRmcm9udC1pbXBvcnQtc3RhY2suanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJjbG91ZGZyb250LWltcG9ydC1zdGFjay50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGlEQUFtQztBQUVuQyx1RUFBeUQ7QUFDekQsdURBQXlDO0FBQ3pDLHVGQUF5RTtBQUV6RTs7O0dBR0c7QUFDSCxNQUFhLHFCQUFzQixTQUFRLEdBQUcsQ0FBQyxLQUFLO0lBQ2xELFlBQVksS0FBZ0IsRUFBRSxFQUFVLEVBQUUsS0FBc0I7UUFDOUQsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFFeEIsOEVBQThFO1FBQzlFLElBQUksQ0FBQywyQkFBMkIsRUFBRSxDQUFDO1FBQ25DLElBQUksQ0FBQyxnQ0FBZ0MsRUFBRSxDQUFDO0lBQzFDLENBQUM7SUFFTywyQkFBMkI7UUFDakMsd0RBQXdEO1FBQ3hELGdFQUFnRTtRQUVoRSwyRUFBMkU7UUFDM0UsTUFBTSxlQUFlLEdBQUcsVUFBVSxDQUFDLFlBQVksQ0FBQywwQkFBMEIsQ0FDeEUsSUFBSSxFQUNKLHlCQUF5QixFQUN6QjtZQUNFLGNBQWMsRUFBRSxnQkFBZ0I7WUFDaEMsVUFBVSxFQUFFLCtCQUErQjtTQUM1QyxDQUNGLENBQUM7UUFFRixpRUFBaUU7UUFDakUsTUFBTSxlQUFlLEdBQUcsVUFBVSxDQUFDLFlBQVksQ0FBQywwQkFBMEIsQ0FDeEUsSUFBSSxFQUNKLHlCQUF5QixFQUN6QjtZQUNFLGNBQWMsRUFBRSxnQkFBZ0I7WUFDaEMsVUFBVSxFQUFFLDhCQUE4QjtTQUMzQyxDQUNGLENBQUM7UUFFRiw2REFBNkQ7UUFDN0QsTUFBTSxtQkFBbUIsR0FBRyxVQUFVLENBQUMsWUFBWSxDQUFDLDBCQUEwQixDQUM1RSxJQUFJLEVBQ0osNkJBQTZCLEVBQzdCO1lBQ0UsY0FBYyxFQUFFLGVBQWU7WUFDL0IsVUFBVSxFQUFFLCtCQUErQjtTQUM1QyxDQUNGLENBQUM7UUFFRiwwRUFBMEU7UUFDMUUsTUFBTSxlQUFlLEdBQUcsVUFBVSxDQUFDLFlBQVksQ0FBQywwQkFBMEIsQ0FDeEUsSUFBSSxFQUNKLHlCQUF5QixFQUN6QjtZQUNFLGNBQWMsRUFBRSxnQkFBZ0I7WUFDaEMsVUFBVSxFQUFFLDhCQUE4QjtTQUMzQyxDQUNGLENBQUM7UUFFRixvRkFBb0Y7UUFDcEYsTUFBTSxXQUFXLEdBQUcsVUFBVSxDQUFDLFlBQVksQ0FBQywwQkFBMEIsQ0FDcEUsSUFBSSxFQUNKLHFCQUFxQixFQUNyQjtZQUNFLGNBQWMsRUFBRSxlQUFlO1lBQy9CLFVBQVUsRUFBRSwrQkFBK0I7U0FDNUMsQ0FDRixDQUFDO1FBRUYsMERBQTBEO1FBQzFELE1BQU0sZUFBZSxHQUFHLFVBQVUsQ0FBQyxZQUFZLENBQUMsMEJBQTBCLENBQ3hFLElBQUksRUFDSix5QkFBeUIsRUFDekI7WUFDRSxjQUFjLEVBQUUsZ0JBQWdCO1lBQ2hDLFVBQVUsRUFBRSwrQkFBK0I7U0FDNUMsQ0FDRixDQUFDO1FBRUYsb0RBQW9EO1FBQ3BELE1BQU0sYUFBYSxHQUFHLFVBQVUsQ0FBQyxZQUFZLENBQUMsMEJBQTBCLENBQ3RFLElBQUksRUFDSix1QkFBdUIsRUFDdkI7WUFDRSxjQUFjLEVBQUUsZ0JBQWdCO1lBQ2hDLFVBQVUsRUFBRSwrQkFBK0I7U0FDNUMsQ0FDRixDQUFDO1FBRUYsa0NBQWtDO1FBQ2xDLE1BQU0sbUJBQW1CLEdBQUcsa0JBQWtCLENBQUMsV0FBVyxDQUFDLGtCQUFrQixDQUMzRSxJQUFJLEVBQ0osOEJBQThCLEVBQzlCLHFGQUFxRixDQUN0RixDQUFDO1FBRUYsNEJBQTRCO1FBQzVCLE1BQU0sZ0JBQWdCLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQy9DLElBQUksRUFDSiw2QkFBNkIsRUFDN0IsZ0JBQWdCLENBQ2pCLENBQUM7UUFFRiw0Q0FBNEM7UUFDNUMsSUFBSSxDQUFDLFVBQVUsQ0FDYixlQUFlLEVBQ2YsZUFBZSxFQUNmLGVBQWUsRUFDZixtQkFBbUIsRUFDbkIsV0FBVyxFQUNYLGVBQWUsRUFDZixhQUFhLEVBQ2IsbUJBQW1CLEVBQ25CLGdCQUFnQixDQUNqQixDQUFDO0lBQ0osQ0FBQztJQUVPLFVBQVUsQ0FDaEIsZUFBeUMsRUFDekMsZUFBeUMsRUFDekMsZUFBeUMsRUFDekMsbUJBQTZDLEVBQzdDLFdBQXFDLEVBQ3JDLGVBQXlDLEVBQ3pDLGFBQXVDLEVBQ3ZDLFdBQTRDLEVBQzVDLFFBQW9CO1FBRXBCLHlEQUF5RDtRQUN6RCxJQUFJLEdBQUcsQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLDJCQUEyQixFQUFFO1lBQ25ELEtBQUssRUFBRSxlQUFlLENBQUMsY0FBYztZQUNyQyxXQUFXLEVBQUUseURBQXlEO1NBQ3ZFLENBQUMsQ0FBQztRQUVILElBQUksR0FBRyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsMkJBQTJCLEVBQUU7WUFDbkQsS0FBSyxFQUFFLGVBQWUsQ0FBQyxjQUFjO1lBQ3JDLFdBQVcsRUFBRSx5REFBeUQ7U0FDdkUsQ0FBQyxDQUFDO1FBRUgsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSwyQkFBMkIsRUFBRTtZQUNuRCxLQUFLLEVBQUUsZUFBZSxDQUFDLGNBQWM7WUFDckMsV0FBVyxFQUFFLHlEQUF5RDtTQUN2RSxDQUFDLENBQUM7UUFFSCxJQUFJLEdBQUcsQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLCtCQUErQixFQUFFO1lBQ3ZELEtBQUssRUFBRSxtQkFBbUIsQ0FBQyxjQUFjO1lBQ3pDLFdBQVcsRUFBRSwyREFBMkQ7U0FDekUsQ0FBQyxDQUFDO1FBRUgsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSx1QkFBdUIsRUFBRTtZQUMvQyxLQUFLLEVBQUUsV0FBVyxDQUFDLGNBQWM7WUFDakMsV0FBVyxFQUFFLGtEQUFrRDtTQUNoRSxDQUFDLENBQUM7UUFFSCxJQUFJLEdBQUcsQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLDJCQUEyQixFQUFFO1lBQ25ELEtBQUssRUFBRSxlQUFlLENBQUMsY0FBYztZQUNyQyxXQUFXLEVBQUUsd0RBQXdEO1NBQ3RFLENBQUMsQ0FBQztRQUVILElBQUksR0FBRyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUseUJBQXlCLEVBQUU7WUFDakQsS0FBSyxFQUFFLGFBQWEsQ0FBQyxjQUFjO1lBQ25DLFdBQVcsRUFBRSxzREFBc0Q7U0FDcEUsQ0FBQyxDQUFDO1FBRUgsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSx3QkFBd0IsRUFBRTtZQUNoRCxLQUFLLEVBQUUsV0FBVyxDQUFDLGNBQWM7WUFDakMsV0FBVyxFQUFFLDhCQUE4QjtTQUM1QyxDQUFDLENBQUM7UUFFSCxJQUFJLEdBQUcsQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLHNCQUFzQixFQUFFO1lBQzlDLEtBQUssRUFBRSxRQUFRLENBQUMsVUFBVTtZQUMxQixXQUFXLEVBQUUseUJBQXlCO1NBQ3ZDLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFTyxnQ0FBZ0M7UUFDdEMsOERBQThEO1FBQzlELGlEQUFpRDtRQUVqRCxJQUFJLEdBQUcsQ0FBQyxPQUFPLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRSwwQkFBMEIsRUFBRTtZQUNoRSxhQUFhLEVBQUUsd0NBQXdDO1lBQ3ZELFdBQVcsRUFBRSw2QkFBNkI7WUFDMUMsV0FBVyxFQUFFLG1FQUFtRTtTQUNqRixDQUFDLENBQUM7UUFFSCxJQUFJLEdBQUcsQ0FBQyxPQUFPLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRSx1QkFBdUIsRUFBRTtZQUM3RCxhQUFhLEVBQUUsb0RBQW9EO1lBQ25FLFdBQVcsRUFBRSxHQUFHO1lBQ2hCLFdBQVcsRUFBRSw0REFBNEQ7U0FDMUUsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztDQUNGO0FBekxELHNEQXlMQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGNkayBmcm9tICdhd3MtY2RrLWxpYic7XG5pbXBvcnQgeyBDb25zdHJ1Y3QgfSBmcm9tICdjb25zdHJ1Y3RzJztcbmltcG9ydCAqIGFzIGNsb3VkZnJvbnQgZnJvbSAnYXdzLWNkay1saWIvYXdzLWNsb3VkZnJvbnQnO1xuaW1wb3J0ICogYXMgczMgZnJvbSAnYXdzLWNkay1saWIvYXdzLXMzJztcbmltcG9ydCAqIGFzIGNlcnRpZmljYXRlbWFuYWdlciBmcm9tICdhd3MtY2RrLWxpYi9hd3MtY2VydGlmaWNhdGVtYW5hZ2VyJztcblxuLyoqXG4gKiBTdGFjayB0aGF0IGltcG9ydHMgZXhpc3RpbmcgQ2xvdWRGcm9udCBkaXN0cmlidXRpb25zIGZyb20gVGVycmFmb3JtLW1hbmFnZWQgaW5mcmFzdHJ1Y3R1cmVcbiAqIFRoaXMgcmVwcmVzZW50cyB0aGUgY3VycmVudCBzdGF0ZSB3aXRob3V0IGNyZWF0aW5nIG5ldyByZXNvdXJjZXMgLSBmb3IgbWlncmF0aW9uIHB1cnBvc2VzXG4gKi9cbmV4cG9ydCBjbGFzcyBDbG91ZEZyb250SW1wb3J0U3RhY2sgZXh0ZW5kcyBjZGsuU3RhY2sge1xuICBjb25zdHJ1Y3RvcihzY29wZTogQ29uc3RydWN0LCBpZDogc3RyaW5nLCBwcm9wcz86IGNkay5TdGFja1Byb3BzKSB7XG4gICAgc3VwZXIoc2NvcGUsIGlkLCBwcm9wcyk7XG5cbiAgICAvLyBJbXBvcnQgZXhpc3RpbmcgQ2xvdWRGcm9udCBkaXN0cmlidXRpb25zIHRvIHJlZmxlY3QgY3VycmVudCBUZXJyYWZvcm0gc3RhdGVcbiAgICB0aGlzLmltcG9ydEV4aXN0aW5nRGlzdHJpYnV0aW9ucygpO1xuICAgIHRoaXMuY3JlYXRlQ29uZmlndXJhdGlvbkRvY3VtZW50YXRpb24oKTtcbiAgfVxuXG4gIHByaXZhdGUgaW1wb3J0RXhpc3RpbmdEaXN0cmlidXRpb25zKCkge1xuICAgIC8vIEltcG9ydCBleGlzdGluZyBDbG91ZEZyb250IGRpc3RyaWJ1dGlvbnMgYnkgdGhlaXIgSURzXG4gICAgLy8gVGhlc2UgcmVwcmVzZW50IHlvdXIgY3VycmVudCBUZXJyYWZvcm0tbWFuYWdlZCBpbmZyYXN0cnVjdHVyZVxuICAgIFxuICAgIC8vIERpc3RyaWJ1dGlvbiBFMVkyR1pYSFhDR0k4SCAtIEFQSSBHYXRld2F5IHdpdGggY3VzdG9tIGhlYWRlcnMgZm9yd2FyZGluZ1xuICAgIGNvbnN0IGFwaUdhdGV3YXlEaXN0MSA9IGNsb3VkZnJvbnQuRGlzdHJpYnV0aW9uLmZyb21EaXN0cmlidXRpb25BdHRyaWJ1dGVzKFxuICAgICAgdGhpcywgXG4gICAgICAnRXhpc3RpbmdBcGlHYXRld2F5RGlzdDEnLCBcbiAgICAgIHtcbiAgICAgICAgZGlzdHJpYnV0aW9uSWQ6ICdFMVkyR1pYSFhDR0k4SCcsXG4gICAgICAgIGRvbWFpbk5hbWU6ICdkMnM3aHJncnoyczFiYS5jbG91ZGZyb250Lm5ldCcsXG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIERpc3RyaWJ1dGlvbiBFMTMxUlo2OVFZTFg3TyAtIEFQSSBHYXRld2F5IHdpdGggY2FjaGUgYmVoYXZpb3JzXG4gICAgY29uc3QgYXBpR2F0ZXdheURpc3QyID0gY2xvdWRmcm9udC5EaXN0cmlidXRpb24uZnJvbURpc3RyaWJ1dGlvbkF0dHJpYnV0ZXMoXG4gICAgICB0aGlzLCBcbiAgICAgICdFeGlzdGluZ0FwaUdhdGV3YXlEaXN0MicsIFxuICAgICAge1xuICAgICAgICBkaXN0cmlidXRpb25JZDogJ0UxMzFSWjY5UVlMWDdPJyxcbiAgICAgICAgZG9tYWluTmFtZTogJ2R0b3IyMXE1eTU2ZGkuY2xvdWRmcm9udC5uZXQnLFxuICAgICAgfVxuICAgICk7XG5cbiAgICAvLyBEaXN0cmlidXRpb24gRVhaQzY2VUtKQUFCSyAtIEV4dGVybmFsIHdlYnNpdGUgZGlzdHJpYnV0aW9uXG4gICAgY29uc3QgZXh0ZXJuYWxXZWJzaXRlRGlzdCA9IGNsb3VkZnJvbnQuRGlzdHJpYnV0aW9uLmZyb21EaXN0cmlidXRpb25BdHRyaWJ1dGVzKFxuICAgICAgdGhpcywgXG4gICAgICAnRXhpc3RpbmdFeHRlcm5hbFdlYnNpdGVEaXN0JywgXG4gICAgICB7XG4gICAgICAgIGRpc3RyaWJ1dGlvbklkOiAnRVhaQzY2VUtKQUFCSycsXG4gICAgICAgIGRvbWFpbk5hbWU6ICdkMXExaTZhZmNqejE4Zi5jbG91ZGZyb250Lm5ldCcsXG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIERpc3RyaWJ1dGlvbiBFMkVaT05UVUQ4REExTyAtIEFQSSBHYXRld2F5IHdpdGggZGV2aWNlIGRldGVjdGlvbiBoZWFkZXJzXG4gICAgY29uc3QgYXBpR2F0ZXdheURpc3QzID0gY2xvdWRmcm9udC5EaXN0cmlidXRpb24uZnJvbURpc3RyaWJ1dGlvbkF0dHJpYnV0ZXMoXG4gICAgICB0aGlzLCBcbiAgICAgICdFeGlzdGluZ0FwaUdhdGV3YXlEaXN0MycsIFxuICAgICAge1xuICAgICAgICBkaXN0cmlidXRpb25JZDogJ0UyRVpPTlRVRDhEQTFPJyxcbiAgICAgICAgZG9tYWluTmFtZTogJ2Q2b21tMjU4aHphcW0uY2xvdWRmcm9udC5uZXQnLFxuICAgICAgfVxuICAgICk7XG5cbiAgICAvLyBEaXN0cmlidXRpb24gRVMyN1NKN1gzVVgxTSAtIENvbXBsZXggbXVsdGktb3JpZ2luIGRpc3RyaWJ1dGlvbiB3aXRoIGN1c3RvbSBkb21haW5cbiAgICBjb25zdCBjb21wbGV4RGlzdCA9IGNsb3VkZnJvbnQuRGlzdHJpYnV0aW9uLmZyb21EaXN0cmlidXRpb25BdHRyaWJ1dGVzKFxuICAgICAgdGhpcywgXG4gICAgICAnRXhpc3RpbmdDb21wbGV4RGlzdCcsIFxuICAgICAge1xuICAgICAgICBkaXN0cmlidXRpb25JZDogJ0VTMjdTSjdYM1VYMU0nLFxuICAgICAgICBkb21haW5OYW1lOiAnZDEyMWd0MzZ0MW8ydmcuY2xvdWRmcm9udC5uZXQnLFxuICAgICAgfVxuICAgICk7XG5cbiAgICAvLyBEaXN0cmlidXRpb24gRTEwTlpGVzlENkFNVTIgLSBNdWx0aS1vcmlnaW4gZGlzdHJpYnV0aW9uXG4gICAgY29uc3QgbXVsdGlPcmlnaW5EaXN0ID0gY2xvdWRmcm9udC5EaXN0cmlidXRpb24uZnJvbURpc3RyaWJ1dGlvbkF0dHJpYnV0ZXMoXG4gICAgICB0aGlzLCBcbiAgICAgICdFeGlzdGluZ011bHRpT3JpZ2luRGlzdCcsIFxuICAgICAge1xuICAgICAgICBkaXN0cmlidXRpb25JZDogJ0UxME5aRlc5RDZBTVUyJyxcbiAgICAgICAgZG9tYWluTmFtZTogJ2QxdmR0dGFjd3F3OTNuLmNsb3VkZnJvbnQubmV0JyxcbiAgICAgIH1cbiAgICApO1xuXG4gICAgLy8gRGlzdHJpYnV0aW9uIEUxWU5XVkhUQUdZMzBUIC0gUzMgV2Vic2l0ZSB3aXRoIFdBRlxuICAgIGNvbnN0IHMzV2Vic2l0ZURpc3QgPSBjbG91ZGZyb250LkRpc3RyaWJ1dGlvbi5mcm9tRGlzdHJpYnV0aW9uQXR0cmlidXRlcyhcbiAgICAgIHRoaXMsIFxuICAgICAgJ0V4aXN0aW5nUzNXZWJzaXRlRGlzdCcsIFxuICAgICAge1xuICAgICAgICBkaXN0cmlidXRpb25JZDogJ0UxWU5XVkhUQUdZMzBUJyxcbiAgICAgICAgZG9tYWluTmFtZTogJ2QzdHFhdXdzNnM3dDZvLmNsb3VkZnJvbnQubmV0JyxcbiAgICAgIH1cbiAgICApO1xuXG4gICAgLy8gSW1wb3J0IGV4aXN0aW5nIEFDTSBjZXJ0aWZpY2F0ZVxuICAgIGNvbnN0IGV4aXN0aW5nQ2VydGlmaWNhdGUgPSBjZXJ0aWZpY2F0ZW1hbmFnZXIuQ2VydGlmaWNhdGUuZnJvbUNlcnRpZmljYXRlQXJuKFxuICAgICAgdGhpcyxcbiAgICAgICdFeGlzdGluZ0RlbHR5VGVzdENlcnRpZmljYXRlJyxcbiAgICAgICdhcm46YXdzOmFjbTp1cy1lYXN0LTE6OTAyNTM3NjU5NDU2OmNlcnRpZmljYXRlLzEzOTZjNzE3LWZmNzYtNDAzYS05YWIxLWNmMjE4NWE4MzE5NidcbiAgICApO1xuXG4gICAgLy8gSW1wb3J0IGV4aXN0aW5nIFMzIGJ1Y2tldFxuICAgIGNvbnN0IGV4aXN0aW5nUzNCdWNrZXQgPSBzMy5CdWNrZXQuZnJvbUJ1Y2tldE5hbWUoXG4gICAgICB0aGlzLCBcbiAgICAgICdFeGlzdGluZ0RlbGV0ZU9ubHlEZXZCdWNrZXQnLCBcbiAgICAgICdkZWxldGVvbmx5LWRldidcbiAgICApO1xuXG4gICAgLy8gU3RvcmUgcmVmZXJlbmNlcyBmb3IgcG90ZW50aWFsIGZ1dHVyZSB1c2VcbiAgICB0aGlzLmFkZE91dHB1dHMoXG4gICAgICBhcGlHYXRld2F5RGlzdDEsXG4gICAgICBhcGlHYXRld2F5RGlzdDIsXG4gICAgICBhcGlHYXRld2F5RGlzdDMsXG4gICAgICBleHRlcm5hbFdlYnNpdGVEaXN0LFxuICAgICAgY29tcGxleERpc3QsXG4gICAgICBtdWx0aU9yaWdpbkRpc3QsXG4gICAgICBzM1dlYnNpdGVEaXN0LFxuICAgICAgZXhpc3RpbmdDZXJ0aWZpY2F0ZSxcbiAgICAgIGV4aXN0aW5nUzNCdWNrZXRcbiAgICApO1xuICB9XG5cbiAgcHJpdmF0ZSBhZGRPdXRwdXRzKFxuICAgIGFwaUdhdGV3YXlEaXN0MTogY2xvdWRmcm9udC5JRGlzdHJpYnV0aW9uLFxuICAgIGFwaUdhdGV3YXlEaXN0MjogY2xvdWRmcm9udC5JRGlzdHJpYnV0aW9uLFxuICAgIGFwaUdhdGV3YXlEaXN0MzogY2xvdWRmcm9udC5JRGlzdHJpYnV0aW9uLFxuICAgIGV4dGVybmFsV2Vic2l0ZURpc3Q6IGNsb3VkZnJvbnQuSURpc3RyaWJ1dGlvbixcbiAgICBjb21wbGV4RGlzdDogY2xvdWRmcm9udC5JRGlzdHJpYnV0aW9uLFxuICAgIG11bHRpT3JpZ2luRGlzdDogY2xvdWRmcm9udC5JRGlzdHJpYnV0aW9uLFxuICAgIHMzV2Vic2l0ZURpc3Q6IGNsb3VkZnJvbnQuSURpc3RyaWJ1dGlvbixcbiAgICBjZXJ0aWZpY2F0ZTogY2VydGlmaWNhdGVtYW5hZ2VyLklDZXJ0aWZpY2F0ZSxcbiAgICBzM0J1Y2tldDogczMuSUJ1Y2tldFxuICApIHtcbiAgICAvLyBPdXRwdXQgZXhpc3RpbmcgZGlzdHJpYnV0aW9uIGluZm9ybWF0aW9uIGZvciByZWZlcmVuY2VcbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdBcGlHYXRld2F5RGlzdDFJZCcsIHtcbiAgICAgIHZhbHVlOiBhcGlHYXRld2F5RGlzdDEuZGlzdHJpYnV0aW9uSWQsXG4gICAgICBkZXNjcmlwdGlvbjogJ0V4aXN0aW5nIEFQSSBHYXRld2F5IERpc3RyaWJ1dGlvbiAxIElEIChFMVkyR1pYSFhDR0k4SCknLFxuICAgIH0pO1xuXG4gICAgbmV3IGNkay5DZm5PdXRwdXQodGhpcywgJ0V4aXN0aW5nQXBpR2F0ZXdheURpc3QySWQnLCB7XG4gICAgICB2YWx1ZTogYXBpR2F0ZXdheURpc3QyLmRpc3RyaWJ1dGlvbklkLFxuICAgICAgZGVzY3JpcHRpb246ICdFeGlzdGluZyBBUEkgR2F0ZXdheSBEaXN0cmlidXRpb24gMiBJRCAoRTEzMVJaNjlRWUxYN08pJyxcbiAgICB9KTtcblxuICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdFeGlzdGluZ0FwaUdhdGV3YXlEaXN0M0lkJywge1xuICAgICAgdmFsdWU6IGFwaUdhdGV3YXlEaXN0My5kaXN0cmlidXRpb25JZCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXhpc3RpbmcgQVBJIEdhdGV3YXkgRGlzdHJpYnV0aW9uIDMgSUQgKEUyRVpPTlRVRDhEQTFPKScsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdFeHRlcm5hbFdlYnNpdGVEaXN0SWQnLCB7XG4gICAgICB2YWx1ZTogZXh0ZXJuYWxXZWJzaXRlRGlzdC5kaXN0cmlidXRpb25JZCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXhpc3RpbmcgRXh0ZXJuYWwgV2Vic2l0ZSBEaXN0cmlidXRpb24gSUQgKEVYWkM2NlVLSkFBQkspJyxcbiAgICB9KTtcblxuICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdFeGlzdGluZ0NvbXBsZXhEaXN0SWQnLCB7XG4gICAgICB2YWx1ZTogY29tcGxleERpc3QuZGlzdHJpYnV0aW9uSWQsXG4gICAgICBkZXNjcmlwdGlvbjogJ0V4aXN0aW5nIENvbXBsZXggRGlzdHJpYnV0aW9uIElEIChFUzI3U0o3WDNVWDFNKScsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdNdWx0aU9yaWdpbkRpc3RJZCcsIHtcbiAgICAgIHZhbHVlOiBtdWx0aU9yaWdpbkRpc3QuZGlzdHJpYnV0aW9uSWQsXG4gICAgICBkZXNjcmlwdGlvbjogJ0V4aXN0aW5nIE11bHRpLU9yaWdpbiBEaXN0cmlidXRpb24gSUQgKEUxME5aRlc5RDZBTVUyKScsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdTM1dlYnNpdGVEaXN0SWQnLCB7XG4gICAgICB2YWx1ZTogczNXZWJzaXRlRGlzdC5kaXN0cmlidXRpb25JZCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXhpc3RpbmcgUzMgV2Vic2l0ZSBEaXN0cmlidXRpb24gSUQgKEUxWU5XVkhUQUdZMzBUKScsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdDZXJ0aWZpY2F0ZUFybicsIHtcbiAgICAgIHZhbHVlOiBjZXJ0aWZpY2F0ZS5jZXJ0aWZpY2F0ZUFybixcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXhpc3RpbmcgQUNNIENlcnRpZmljYXRlIEFSTicsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnRXhpc3RpbmdTM0J1Y2tldE5hbWUnLCB7XG4gICAgICB2YWx1ZTogczNCdWNrZXQuYnVja2V0TmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXhpc3RpbmcgUzMgQnVja2V0IE5hbWUnLFxuICAgIH0pO1xuICB9XG5cbiAgcHJpdmF0ZSBjcmVhdGVDb25maWd1cmF0aW9uRG9jdW1lbnRhdGlvbigpIHtcbiAgICAvLyBDcmVhdGUgU1NNIHBhcmFtZXRlcnMgdG8gZG9jdW1lbnQgdGhlIGN1cnJlbnQgY29uZmlndXJhdGlvblxuICAgIC8vIFRoaXMgaGVscHMgd2l0aCB0aGUgVGVycmFmb3JtIHRvIENESyBtaWdyYXRpb25cbiAgICBcbiAgICBuZXcgY2RrLmF3c19zc20uU3RyaW5nUGFyYW1ldGVyKHRoaXMsICdUZXJyYWZvcm1NaWdyYXRpb25TdGF0dXMnLCB7XG4gICAgICBwYXJhbWV0ZXJOYW1lOiAnL2Nsb3VkZnJvbnQvdGVycmFmb3JtLW1pZ3JhdGlvbi9zdGF0dXMnLFxuICAgICAgc3RyaW5nVmFsdWU6ICdpbXBvcnRlZC1leGlzdGluZy1yZXNvdXJjZXMnLFxuICAgICAgZGVzY3JpcHRpb246ICdTdGF0dXMgb2YgVGVycmFmb3JtIHRvIENESyBtaWdyYXRpb24gZm9yIENsb3VkRnJvbnQgZGlzdHJpYnV0aW9ucycsXG4gICAgfSk7XG5cbiAgICBuZXcgY2RrLmF3c19zc20uU3RyaW5nUGFyYW1ldGVyKHRoaXMsICdEaXN0cmlidXRpb25JbnZlbnRvcnknLCB7XG4gICAgICBwYXJhbWV0ZXJOYW1lOiAnL2Nsb3VkZnJvbnQvdGVycmFmb3JtLW1pZ3JhdGlvbi9kaXN0cmlidXRpb24tY291bnQnLFxuICAgICAgc3RyaW5nVmFsdWU6ICc3JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnTnVtYmVyIG9mIENsb3VkRnJvbnQgZGlzdHJpYnV0aW9ucyBpbXBvcnRlZCBmcm9tIFRlcnJhZm9ybScsXG4gICAgfSk7XG4gIH1cbn1cbiJdfQ==