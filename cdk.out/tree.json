{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.200.1"}, "children": {"CloudFrontImportStack": {"id": "CloudFrontImportStack", "path": "CloudFrontImportStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"ExistingApiGatewayDist1": {"id": "ExistingApiGatewayDist1", "path": "CloudFrontImportStack/ExistingApiGatewayDist1", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingApiGatewayDist2": {"id": "ExistingApiGatewayDist2", "path": "CloudFrontImportStack/ExistingApiGatewayDist2", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingExternalWebsiteDist": {"id": "ExistingExternalWebsiteDist", "path": "CloudFrontImportStack/ExistingExternalWebsiteDist", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingApiGatewayDist3": {"id": "ExistingApiGatewayDist3", "path": "CloudFrontImportStack/ExistingApiGatewayDist3", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingComplexDist": {"id": "ExistingComplexDist", "path": "CloudFrontImportStack/ExistingComplexDist", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingMultiOriginDist": {"id": "ExistingMultiOriginDist", "path": "CloudFrontImportStack/ExistingMultiOriginDist", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingS3WebsiteDist": {"id": "ExistingS3WebsiteDist", "path": "CloudFrontImportStack/ExistingS3WebsiteDist", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingDeltyTestCertificate": {"id": "ExistingDeltyTestCertificate", "path": "CloudFrontImportStack/ExistingDeltyTestCertificate", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "ExistingDeleteOnlyDevBucket": {"id": "ExistingDeleteOnlyDevBucket", "path": "CloudFrontImportStack/ExistingDeleteOnlyDevBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.200.1", "metadata": []}}, "ExistingApiGatewayDist1Id": {"id": "ExistingApiGatewayDist1Id", "path": "CloudFrontImportStack/ExistingApiGatewayDist1Id", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingApiGatewayDist2Id": {"id": "ExistingApiGatewayDist2Id", "path": "CloudFrontImportStack/ExistingApiGatewayDist2Id", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingApiGatewayDist3Id": {"id": "ExistingApiGatewayDist3Id", "path": "CloudFrontImportStack/ExistingApiGatewayDist3Id", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingExternalWebsiteDistId": {"id": "ExistingExternalWebsiteDistId", "path": "CloudFrontImportStack/ExistingExternalWebsiteDistId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingComplexDistId": {"id": "ExistingComplexDistId", "path": "CloudFrontImportStack/ExistingComplexDistId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingMultiOriginDistId": {"id": "ExistingMultiOriginDistId", "path": "CloudFrontImportStack/ExistingMultiOriginDistId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingS3WebsiteDistId": {"id": "ExistingS3WebsiteDistId", "path": "CloudFrontImportStack/ExistingS3WebsiteDistId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingCertificateArn": {"id": "ExistingCertificateArn", "path": "CloudFrontImportStack/ExistingCertificateArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ExistingS3BucketName": {"id": "ExistingS3BucketName", "path": "CloudFrontImportStack/ExistingS3BucketName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "TerraformMigrationStatus": {"id": "TerraformMigrationStatus", "path": "CloudFrontImportStack/TerraformMigrationStatus", "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.200.1", "metadata": [{"parameterName": "*", "stringValue": "*", "description": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "CloudFrontImportStack/TerraformMigrationStatus/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"description": "Status of Terraform to CDK migration for CloudFront distributions", "name": "/cloudfront/terraform-migration/status", "type": "String", "value": "imported-existing-resources"}}}}}, "DistributionInventory": {"id": "DistributionInventory", "path": "CloudFrontImportStack/DistributionInventory", "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.200.1", "metadata": [{"parameterName": "*", "stringValue": "*", "description": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "CloudFrontImportStack/DistributionInventory/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.200.1"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"description": "Number of CloudFront distributions imported from Terraform", "name": "/cloudfront/terraform-migration/distribution-count", "type": "String", "value": "7"}}}}}, "CDKMetadata": {"id": "CDKMetadata", "path": "CloudFrontImportStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "CloudFrontImportStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "CloudFrontImportStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "CloudFrontImportStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}