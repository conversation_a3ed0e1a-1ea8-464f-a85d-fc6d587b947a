type IoMessageCodeCategory = 'TOOLKIT' | 'SDK' | 'ASSETS';
type IoCodeLevel = 'E' | 'W' | 'I';
type IoMessageSpecificCode<L extends IoCodeLevel> = `CDK_${IoMessageCodeCategory}_${L}${number}${number}${number}${number}`;
interface LogParams<L extends IoCodeLevel> {
    /**
     * Message code
     */
    readonly code: IoMessageSpecificCode<L>;
    /**
     * Message
     */
    readonly message: string;
}
type LogInput<L extends IoCodeLevel> = string | LogParams<L>;
/**
 * Logs an error level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * error(`operation failed: ${e}`) // infers default error code `CDK_TOOLKIT_E000`
 * error('operation failed: %s', e) // infers default error code `CDK_TOOLKIT_E000`
 * error({ message: 'operation failed', code: 'CDK_SDK_E001' }) // specifies error code `CDK_SDK_E001`
 * error({ message: 'operation failed: %s', code: 'CDK_SDK_E001' }, e) // specifies error code `CDK_SDK_E001`
 * ```
 */
export declare const error: (input: LogInput<"E">, ...args: unknown[]) => void;
/**
 * Logs an warning level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * warning(`deprected feature: ${message}`) // infers default warning code `CDK_TOOLKIT_W000`
 * warning('deprected feature: %s', message) // infers default warning code `CDK_TOOLKIT_W000`
 * warning({ message: 'deprected feature', code: 'CDK_SDK_W001' }) // specifies warning code `CDK_SDK_W001`
 * warning({ message: 'deprected feature: %s', code: 'CDK_SDK_W001' }, message) // specifies warning code `CDK_SDK_W001`
 * ```
 */
export declare const warning: (input: LogInput<"W">, ...args: unknown[]) => void;
/**
 * Logs an info level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * info(`processing: ${message}`) // infers default info code `CDK_TOOLKIT_I000`
 * info('processing: %s', message) // infers default info code `CDK_TOOLKIT_I000`
 * info({ message: 'processing', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * info({ message: 'processing: %s', code: 'CDK_TOOLKIT_I001' }, message) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
export declare const info: (input: LogInput<"I">, ...args: unknown[]) => void;
/**
 * Logs an result. In the CLI, this always goes to stdout.
 *
 * Can be used in multiple ways:
 * ```ts
 * result(`${JSON.stringify(stats)}`) // infers default info code `CDK_TOOLKIT_I000`
 * result('{"count": %d}', count) // infers default info code `CDK_TOOLKIT_I000`
 * result({ message: 'stats: %j', code: 'CDK_DATA_I001' }) // specifies info code `CDK_DATA_I001`
 * result({ message: 'stats: %j', code: 'CDK_DATA_I001' }, stats) // specifies info code `CDK_DATA_I001`
 * ```
 */
export declare const result: (input: LogInput<"I">, ...args: unknown[]) => void;
/**
 * Logs a debug level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * debug(`state: ${JSON.stringify(state)}`) // infers default info code `CDK_TOOLKIT_I000`
 * debug('cache hit ratio: %d%%', ratio) // infers default info code `CDK_TOOLKIT_I000`
 * debug({ message: 'state update', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * debug({ message: 'ratio: %d%%', code: 'CDK_TOOLKIT_I001' }, ratio) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
export declare const debug: (input: LogInput<"I">, ...args: unknown[]) => void;
/**
 * Logs an info level success message in green text.
 *
 * Can be used in multiple ways:
 * ```ts
 * success(`deployment completed: ${name}`) // infers default info code `CDK_TOOLKIT_I000`
 * success('processed %d items', count) // infers default info code `CDK_TOOLKIT_I000`
 * success({ message: 'completed', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * success({ message: 'items: %d', code: 'CDK_TOOLKIT_I001' }, count) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
export declare const success: (input: LogInput<"I">, ...args: unknown[]) => void;
/**
 * Logs an info level message in bold text.
 *
 * Can be used in multiple ways:
 * ```ts
 * highlight(`important: ${msg}`) // infers default info code `CDK_TOOLKIT_I000`
 * highlight('attention required: %s', reason) // infers default info code `CDK_TOOLKIT_I000`
 * highlight({ message: 'notice', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * highlight({ message: 'notice: %s', code: 'CDK_TOOLKIT_I001' }, msg) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
export declare const highlight: (input: LogInput<"I">, ...args: unknown[]) => void;
export {};
