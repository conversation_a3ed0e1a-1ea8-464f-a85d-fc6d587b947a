"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.doctor = doctor;
const process = require("process");
const cxapi = require("@aws-cdk/cx-api");
const chalk = require("chalk");
const logging_1 = require("../../lib/logging");
const version = require("../cli/version");
async function doctor() {
    let exitStatus = 0;
    for (const verification of verifications) {
        if (!await verification()) {
            exitStatus = -1;
        }
    }
    await version.displayVersionMessage();
    return exitStatus;
}
const verifications = [
    displayVersionInformation,
    displayAwsEnvironmentVariables,
    displayCdkEnvironmentVariables,
];
// ### Verifications ###
function displayVersionInformation() {
    (0, logging_1.info)(`ℹ️ CDK Version: ${chalk.green(version.displayVersion())}`);
    return true;
}
function displayAwsEnvironmentVariables() {
    const keys = Object.keys(process.env).filter(s => s.startsWith('AWS_'));
    if (keys.length === 0) {
        (0, logging_1.info)('ℹ️ No AWS environment variables');
        return true;
    }
    (0, logging_1.info)('ℹ️ AWS environment variables:');
    for (const key of keys) {
        (0, logging_1.info)(`  - ${chalk.blue(key)} = ${chalk.green(anonymizeAwsVariable(key, process.env[key]))}`);
    }
    return true;
}
function displayCdkEnvironmentVariables() {
    const keys = Object.keys(process.env).filter(s => s.startsWith('CDK_'));
    if (keys.length === 0) {
        (0, logging_1.info)('ℹ️ No CDK environment variables');
        return true;
    }
    (0, logging_1.info)('ℹ️ CDK environment variables:');
    let healthy = true;
    for (const key of keys.sort()) {
        if (key === cxapi.CONTEXT_ENV || key === cxapi.CONTEXT_OVERFLOW_LOCATION_ENV || key === cxapi.OUTDIR_ENV) {
            (0, logging_1.info)(`  - ${chalk.red(key)} = ${chalk.green(process.env[key])} (⚠️ reserved for use by the CDK toolkit)`);
            healthy = false;
        }
        else {
            (0, logging_1.info)(`  - ${chalk.blue(key)} = ${chalk.green(process.env[key])}`);
        }
    }
    return healthy;
}
function anonymizeAwsVariable(name, value) {
    if (name === 'AWS_ACCESS_KEY_ID') {
        return value.slice(0, 4) + '<redacted>';
    } // Show ASIA/AKIA key type, but hide identifier
    if (name === 'AWS_SECRET_ACCESS_KEY' || name === 'AWS_SESSION_TOKEN' || name === 'AWS_SECURITY_TOKEN') {
        return '<redacted>';
    }
    return value;
}
//# sourceMappingURL=data:application/json;base64,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