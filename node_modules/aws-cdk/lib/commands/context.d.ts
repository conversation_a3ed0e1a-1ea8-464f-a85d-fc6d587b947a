import type { Context } from '../api/context';
/**
 * Options for the context command
 */
export interface ContextOptions {
    /**
     * The context object sourced from all context locations
     */
    context: Context;
    /**
     * The context key (or its index) to reset
     *
     * @default undefined
     */
    reset?: string;
    /**
     * Ignore missing key error
     *
     * @default false
     */
    force?: boolean;
    /**
     * Clear all context
     *
     * @default false
     */
    clear?: boolean;
    /**
     * Use JSON output instead of YAML when templates are printed to STDOUT
     *
     * @default false
     */
    json?: boolean;
}
export declare function contextHandler(options: ContextOptions): Promise<number>;
