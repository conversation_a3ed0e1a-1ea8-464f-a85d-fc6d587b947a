"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CliIoHost = void 0;
exports.isCI = isCI;
const util = require("node:util");
const cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const promptly = require("promptly");
const api_private_1 = require("../../../lib/api-private");
const deploy_1 = require("../../commands/deploy");
/**
 * A simple IO host for the CLI that writes messages to the console.
 */
class CliIoHost {
    /**
     * Returns the singleton instance
     */
    static instance(props = {}, forceNew = false) {
        if (forceNew || !CliIoHost._instance) {
            CliIoHost._instance = new CliIoHost(props);
        }
        return CliIoHost._instance;
    }
    constructor(props = {}) {
        /**
         * Configure the target stream for notices
         *
         * (Not a setter because there's no need for additional logic when this value
         * is changed yet)
         */
        this.noticesDestination = 'stderr';
        this._progress = deploy_1.StackActivityProgress.BAR;
        // Corked Logging
        this.corkedCounter = 0;
        this.corkedLoggingBuffer = [];
        this.currentAction = props.currentAction ?? 'none';
        this.isTTY = props.isTTY ?? process.stdout.isTTY ?? false;
        this.logLevel = props.logLevel ?? 'info';
        this.isCI = props.isCI ?? isCI();
        this.requireDeployApproval = props.requireDeployApproval ?? cloud_assembly_schema_1.RequireApproval.BROADENING;
        this.stackProgress = props.stackProgress ?? deploy_1.StackActivityProgress.BAR;
    }
    /**
     * Returns the singleton instance
     */
    registerIoHost(ioHost) {
        if (ioHost !== this) {
            this._internalIoHost = ioHost;
        }
    }
    /**
     * Update the stackProgress preference.
     */
    set stackProgress(type) {
        this._progress = type;
    }
    /**
     * Gets the stackProgress value.
     *
     * This takes into account other state of the ioHost,
     * like if isTTY and isCI.
     */
    get stackProgress() {
        // We can always use EVENTS
        if (this._progress === deploy_1.StackActivityProgress.EVENTS) {
            return this._progress;
        }
        // if a debug message (and thus any more verbose messages) are relevant to the current log level, we have verbose logging
        const verboseLogging = (0, api_private_1.isMessageRelevantForLevel)({ level: 'debug' }, this.logLevel);
        if (verboseLogging) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // On Windows we cannot use fancy output
        const isWindows = process.platform === 'win32';
        if (isWindows) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // On some CI systems (such as CircleCI) output still reports as a TTY so we also
        // need an individual check for whether we're running on CI.
        // see: https://discuss.circleci.com/t/circleci-terminal-is-a-tty-but-term-is-not-set/9965
        const fancyOutputAvailable = this.isTTY && !this.isCI;
        if (!fancyOutputAvailable) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // Use the user preference
        return this._progress;
    }
    get defaults() {
        return this.asIoHelper().defaults;
    }
    asIoHelper() {
        return (0, api_private_1.asIoHelper)(this, this.currentAction);
    }
    /**
     * Executes a block of code with corked logging. All log messages during execution
     * are buffered and only written when all nested cork blocks complete (when CORK_COUNTER reaches 0).
     * The corking is bound to the specific instance of the CliIoHost.
     *
     * @param block - Async function to execute with corked logging
     * @returns Promise that resolves with the block's return value
     */
    async withCorkedLogging(block) {
        this.corkedCounter++;
        try {
            return await block();
        }
        finally {
            this.corkedCounter--;
            if (this.corkedCounter === 0) {
                // Process each buffered message through notify
                for (const ioMessage of this.corkedLoggingBuffer) {
                    await this.notify(ioMessage);
                }
                // remove all buffered messages in-place
                this.corkedLoggingBuffer.splice(0);
            }
        }
    }
    /**
     * Notifies the host of a message.
     * The caller waits until the notification completes.
     */
    async notify(msg) {
        if (this._internalIoHost) {
            return this._internalIoHost.notify(msg);
        }
        if (this.isStackActivity(msg)) {
            if (!this.activityPrinter) {
                this.activityPrinter = this.makeActivityPrinter();
            }
            await this.activityPrinter.notify(msg);
            return;
        }
        if (!(0, api_private_1.isMessageRelevantForLevel)(msg, this.logLevel)) {
            return;
        }
        if (this.corkedCounter > 0) {
            this.corkedLoggingBuffer.push(msg);
            return;
        }
        const output = this.formatMessage(msg);
        const stream = this.selectStream(msg);
        stream?.write(output);
    }
    /**
     * Detect stack activity messages so they can be send to the printer.
     */
    isStackActivity(msg) {
        return msg.code && [
            'CDK_TOOLKIT_I5501',
            'CDK_TOOLKIT_I5502',
            'CDK_TOOLKIT_I5503',
        ].includes(msg.code);
    }
    /**
     * Detect special messages encode information about whether or not
     * they require approval
     */
    skipApprovalStep(msg) {
        const approvalToolkitCodes = ['CDK_TOOLKIT_I5060'];
        if (!(msg.code && approvalToolkitCodes.includes(msg.code))) {
            false;
        }
        switch (this.requireDeployApproval) {
            // Never require approval
            case cloud_assembly_schema_1.RequireApproval.NEVER:
                return true;
            // Always require approval
            case cloud_assembly_schema_1.RequireApproval.ANYCHANGE:
                return false;
            // Require approval if changes include broadening permissions
            case cloud_assembly_schema_1.RequireApproval.BROADENING:
                return ['none', 'non-broadening'].includes(msg.data?.permissionChangeType);
        }
    }
    /**
     * Determines the output stream, based on message and configuration.
     */
    selectStream(msg) {
        if (isNoticesMessage(msg)) {
            return targetStreamObject(this.noticesDestination);
        }
        return this.selectStreamFromLevel(msg.level);
    }
    /**
     * Determines the output stream, based on message level and configuration.
     */
    selectStreamFromLevel(level) {
        // The stream selection policy for the CLI is the following:
        //
        //   (1) Messages of level `result` always go to `stdout`
        //   (2) Messages of level `error` always go to `stderr`.
        //   (3a) All remaining messages go to `stderr`.
        //   (3b) If we are in CI mode, all remaining messages go to `stdout`.
        //
        switch (level) {
            case 'error':
                return process.stderr;
            case 'result':
                return process.stdout;
            default:
                return this.isCI ? process.stdout : process.stderr;
        }
    }
    /**
     * Notifies the host of a message that requires a response.
     *
     * If the host does not return a response the suggested
     * default response from the input message will be used.
     */
    async requestResponse(msg) {
        // First call out to a registered instance if we have one
        if (this._internalIoHost) {
            return this._internalIoHost.requestResponse(msg);
        }
        // If the request cannot be prompted for by the CliIoHost, we just accept the default
        if (!isPromptableRequest(msg)) {
            await this.notify(msg);
            return msg.defaultResponse;
        }
        const response = await this.withCorkedLogging(async () => {
            // prepare prompt data
            // @todo this format is not defined anywhere, probably should be
            const data = msg.data ?? {};
            const motivation = data.motivation ?? 'User input is needed';
            const concurrency = data.concurrency ?? 0;
            const responseDescription = data.responseDescription;
            // only talk to user if STDIN is a terminal (otherwise, fail)
            if (!this.isTTY) {
                throw new toolkit_lib_1.ToolkitError(`${motivation}, but terminal (TTY) is not attached so we are unable to get a confirmation from the user`);
            }
            // only talk to user if concurrency is 1 (otherwise, fail)
            if (concurrency > 1) {
                throw new toolkit_lib_1.ToolkitError(`${motivation}, but concurrency is greater than 1 so we are unable to get a confirmation from the user`);
            }
            // Special approval prompt
            // Determine if the message needs approval. If it does, continue (it is a basic confirmation prompt)
            // If it does not, return success (true). We only check messages with codes that we are aware
            // are requires approval codes.
            if (this.skipApprovalStep(msg)) {
                return true;
            }
            // Basic confirmation prompt
            // We treat all requests with a boolean response as confirmation prompts
            if (isConfirmationPrompt(msg)) {
                const confirmed = await promptly.confirm(`${chalk.cyan(msg.message)} (y/n)`);
                if (!confirmed) {
                    throw new toolkit_lib_1.ToolkitError('Aborted by user');
                }
                return confirmed;
            }
            // Asking for a specific value
            const prompt = extractPromptInfo(msg);
            const desc = responseDescription ?? prompt.default;
            const answer = await promptly.prompt(`${chalk.cyan(msg.message)}${desc ? ` (${desc})` : ''}`, {
                default: prompt.default,
                trim: true,
            });
            return prompt.convertAnswer(answer);
        });
        // We need to cast this because it is impossible to narrow the generic type
        // isPromptableRequest ensures that the response type is one we can prompt for
        // the remaining code ensure we are indeed returning the correct type
        return response;
    }
    /**
     * Formats a message for console output with optional color support
     */
    formatMessage(msg) {
        // apply provided style or a default style if we're in TTY mode
        let message_text = this.isTTY
            ? styleMap[msg.level](msg.message)
            : msg.message;
        // prepend timestamp if IoMessageLevel is DEBUG or TRACE. Postpend a newline.
        return ((msg.level === 'debug' || msg.level === 'trace')
            ? `[${this.formatTime(msg.time)}] ${message_text}`
            : message_text) + '\n';
    }
    /**
     * Formats date to HH:MM:SS
     */
    formatTime(d) {
        const pad = (n) => n.toString().padStart(2, '0');
        return `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
    }
    /**
     * Get an instance of the ActivityPrinter
     */
    makeActivityPrinter() {
        const props = {
            stream: this.selectStreamFromLevel('info'),
        };
        switch (this.stackProgress) {
            case deploy_1.StackActivityProgress.EVENTS:
                return new api_private_1.HistoryActivityPrinter(props);
            case deploy_1.StackActivityProgress.BAR:
                return new api_private_1.CurrentActivityPrinter(props);
        }
    }
}
exports.CliIoHost = CliIoHost;
/**
 * This IoHost implementation considers a request promptable, if:
 * - it's a yes/no confirmation
 * - asking for a string or number value
 */
function isPromptableRequest(msg) {
    return isConfirmationPrompt(msg)
        || typeof msg.defaultResponse === 'string'
        || typeof msg.defaultResponse === 'number';
}
/**
 * Check if the request is a confirmation prompt
 * We treat all requests with a boolean response as confirmation prompts
 */
function isConfirmationPrompt(msg) {
    return typeof msg.defaultResponse === 'boolean';
}
/**
 * Helper to extract information for promptly from the request
 */
function extractPromptInfo(msg) {
    const isNumber = (typeof msg.defaultResponse === 'number');
    const defaultResponse = util.format(msg.defaultResponse);
    return {
        default: defaultResponse,
        defaultDesc: 'defaultDescription' in msg && msg.defaultDescription ? util.format(msg.defaultDescription) : defaultResponse,
        convertAnswer: isNumber ? (v) => Number(v) : (v) => String(v),
    };
}
const styleMap = {
    error: chalk.red,
    warn: chalk.yellow,
    result: chalk.white,
    info: chalk.white,
    debug: chalk.gray,
    trace: chalk.gray,
};
/**
 * Returns true if the current process is running in a CI environment
 * @returns true if the current process is running in a CI environment
 */
function isCI() {
    return process.env.CI !== undefined && process.env.CI !== 'false' && process.env.CI !== '0';
}
function targetStreamObject(x) {
    switch (x) {
        case 'stderr':
            return process.stderr;
        case 'stdout':
            return process.stdout;
        case 'drop':
            return undefined;
    }
}
function isNoticesMessage(msg) {
    return api_private_1.IO.CDK_TOOLKIT_I0100.is(msg) || api_private_1.IO.CDK_TOOLKIT_W0101.is(msg) || api_private_1.IO.CDK_TOOLKIT_E0101.is(msg) || api_private_1.IO.CDK_TOOLKIT_I0101.is(msg);
}
//# sourceMappingURL=data:application/json;base64,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