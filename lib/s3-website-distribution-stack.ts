import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';

export class S3WebsiteDistributionStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create a WAF Web ACL (recreating the existing one)
    const webAcl = new wafv2.CfnWebACL(this, 'CloudFrontWebACL', {
      scope: 'CLOUDFRONT',
      defaultAction: { allow: {} },
      name: 'CreatedByCloudFront-MikesWebsite-WebACL',
      description: 'Web ACL for Mikes Website CloudFront Distribution',
      rules: [
        {
          name: 'AWSManagedRulesCommonRuleSet',
          priority: 1,
          overrideAction: { none: {} },
          statement: {
            managedRuleGroupStatement: {
              vendorName: 'AWS',
              name: 'AWSManagedRulesCommonRuleSet',
            },
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'CommonRuleSetMetric',
          },
        },
        {
          name: 'AWSManagedRulesKnownBadInputsRuleSet',
          priority: 2,
          overrideAction: { none: {} },
          statement: {
            managedRuleGroupStatement: {
              vendorName: 'AWS',
              name: 'AWSManagedRulesKnownBadInputsRuleSet',
            },
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'KnownBadInputsRuleSetMetric',
          },
        },
      ],
      visibilityConfig: {
        sampledRequestsEnabled: true,
        cloudWatchMetricsEnabled: true,
        metricName: 'MikesWebsiteWebACL',
      },
    });

    // Distribution E1YNWVHTAGY30T - S3 Website with WAF
    const s3WebsiteDistribution = new cloudfront.Distribution(this, 'S3WebsiteDistribution', {
      comment: 'S3 Website Distribution - E1YNWVHTAGY30T Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      webAclId: webAcl.attrArn,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('mikeswebsite.s3-website-us-east-1.amazonaws.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: true,
        // Using the managed caching optimized cache policy
        cachePolicy: cloudfront.CachePolicy.fromCachePolicyId(
          this, 
          'CachingOptimized', 
          '658327ea-f89d-4fab-a63d-7e88639e58f6'
        ),
      },
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Distribution EXZC66UKJAABK - External website distribution
    const externalWebsiteDistribution = new cloudfront.Distribution(this, 'ExternalWebsiteDistribution', {
      comment: 'External Website Distribution - EXZC66UKJAABK Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('quotewizard.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ExternalWebsiteCache', {
          cachePolicyName: 'ExternalWebsiteCachePolicy',
          comment: 'Custom cache policy for external website',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(86400),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Distribution E10NZFW9D6AMU2 - Multi-origin distribution
    const multiOriginDistribution = new cloudfront.Distribution(this, 'MultiOriginDistribution', {
      comment: 'Multi-Origin Distribution - E10NZFW9D6AMU2 Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('cbth.quotewizard.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'MultiOriginCache', {
          cachePolicyName: 'MultiOriginCachePolicy',
          comment: 'Custom cache policy for multi-origin distribution',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(31536000),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
          cookieBehavior: cloudfront.CacheCookieBehavior.all(),
        }),
      },
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Output the distribution domain names
    new cdk.CfnOutput(this, 'S3WebsiteDistributionDomainName', {
      value: s3WebsiteDistribution.distributionDomainName,
      description: 'Domain name for S3 Website Distribution',
    });

    new cdk.CfnOutput(this, 'ExternalWebsiteDistributionDomainName', {
      value: externalWebsiteDistribution.distributionDomainName,
      description: 'Domain name for External Website Distribution',
    });

    new cdk.CfnOutput(this, 'MultiOriginDistributionDomainName', {
      value: multiOriginDistribution.distributionDomainName,
      description: 'Domain name for Multi-Origin Distribution',
    });

    new cdk.CfnOutput(this, 'WebACLArn', {
      value: webAcl.attrArn,
      description: 'ARN of the WAF Web ACL',
    });
  }
}
