import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';

export class ApiGatewayDistributionStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Distribution E1Y2GZXHXCGI8H - API Gateway with custom headers forwarding
    const apiGatewayDistribution1 = new cloudfront.Distribution(this, 'ApiGatewayDistribution1', {
      comment: 'API Gateway Distribution - E1Y2GZXHXCGI8H Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP1_1,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: false,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('p0z7suimt8.execute-api.us-east-1.amazonaws.com', {
          originPath: '/dev',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache1', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy1',
          comment: 'Custom cache policy for API Gateway distribution 1',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList('*'),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.allowList('Accept', 'Origin', 'Referrer'),
        }),
      },
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Distribution E131RZ69QYLX7O - API Gateway with cache behaviors
    const apiGatewayDistribution2 = new cloudfront.Distribution(this, 'ApiGatewayDistribution2', {
      comment: 'API Gateway Distribution with Cache Behaviors - E131RZ69QYLX7O Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP1_1,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: false,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
          originPath: '/prod/regionTest',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache2', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy2',
          comment: 'Custom cache policy for API Gateway distribution 2',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'CloudFront-Is-Tablet-Viewer',
            'CloudFront-Is-Mobile-Viewer', 
            'CloudFront-Is-SmartTV-Viewer',
            'CloudFront-Is-Desktop-Viewer'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },
      
      additionalBehaviors: {
        'Dir*/*.jpg': {
          origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
            originPath: '/prod/regionTest',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'JpgCachePolicy', {
            cachePolicyName: 'JpgFilesCachePolicy',
            comment: 'Cache policy for JPG files',
            defaultTtl: cdk.Duration.seconds(86400),
            maxTtl: cdk.Duration.seconds(31536000),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
          }),
        },
      },
      
      errorResponses: [
        {
          httpStatus: 403,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 404,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 502,
          ttl: cdk.Duration.seconds(0),
        },
        {
          httpStatus: 504,
          ttl: cdk.Duration.seconds(0),
        },
      ],
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Distribution E2EZONTUD8DA1O - API Gateway with device detection headers
    const apiGatewayDistribution3 = new cloudfront.Distribution(this, 'ApiGatewayDistribution3', {
      comment: 'API Gateway Distribution with Device Detection - E2EZONTUD8DA1O Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      
      defaultBehavior: {
        origin: new origins.HttpOrigin('9uerlzte0e.execute-api.us-east-1.amazonaws.com', {
          originPath: '/test/testedge',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache3', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy3',
          comment: 'Custom cache policy for API Gateway distribution 3',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(31536000),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'CloudFront-Viewer-Country',
            'CloudFront-Forwarded-Proto',
            'CloudFront-Is-Tablet-Viewer',
            'CloudFront-Is-Mobile-Viewer',
            'User-Agent',
            'CloudFront-Is-SmartTV-Viewer',
            'CloudFront-Is-Desktop-Viewer'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },
      
      certificate: undefined, // Uses CloudFront default certificate
      domainNames: [], // No custom domain names
      
      // No geo restrictions (equivalent to "none" in the original distribution)
    });

    // Output the distribution domain names
    new cdk.CfnOutput(this, 'ApiGatewayDistribution1DomainName', {
      value: apiGatewayDistribution1.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 1',
    });

    new cdk.CfnOutput(this, 'ApiGatewayDistribution2DomainName', {
      value: apiGatewayDistribution2.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 2',
    });

    new cdk.CfnOutput(this, 'ApiGatewayDistribution3DomainName', {
      value: apiGatewayDistribution3.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 3',
    });
  }
}
