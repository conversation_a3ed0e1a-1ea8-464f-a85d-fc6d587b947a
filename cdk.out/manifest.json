{"version": "44.0.0", "artifacts": {"CloudFrontImportStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "CloudFrontImportStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "CloudFrontImportStack": {"type": "aws:cloudformation:stack", "environment": "aws://902537659456/us-east-1", "properties": {"templateFile": "CloudFrontImportStack.template.json", "terminationProtection": false, "tags": {"Environment": "Development", "ManagedBy": "CDK", "Project": "Terraform to CDK Migration", "Purpose": "Import-Existing-Resources"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-deploy-role-902537659456-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-cfn-exec-role-902537659456-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-902537659456-us-east-1/345cec9b70ee28ddc7a318a3307030576ceed6dc1d08fa3ad36fbf3038bf04c9.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["CloudFrontImportStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::902537659456:role/cdk-hnb659fds-lookup-role-902537659456-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["CloudFrontImportStack.assets"], "metadata": {"/CloudFrontImportStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "Development"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "Terraform to CDK Migration"}, {"Key": "Purpose", "Value": "Import-Existing-Resources"}]}], "/CloudFrontImportStack/ExistingApiGatewayDist1Id": [{"type": "aws:cdk:logicalId", "data": "ExistingApiGatewayDist1Id"}], "/CloudFrontImportStack/ExistingApiGatewayDist2Id": [{"type": "aws:cdk:logicalId", "data": "ExistingApiGatewayDist2Id"}], "/CloudFrontImportStack/ExistingApiGatewayDist3Id": [{"type": "aws:cdk:logicalId", "data": "ExistingApiGatewayDist3Id"}], "/CloudFrontImportStack/ExistingExternalWebsiteDistId": [{"type": "aws:cdk:logicalId", "data": "ExistingExternalWebsiteDistId"}], "/CloudFrontImportStack/ExistingComplexDistId": [{"type": "aws:cdk:logicalId", "data": "ExistingComplexDistId"}], "/CloudFrontImportStack/ExistingMultiOriginDistId": [{"type": "aws:cdk:logicalId", "data": "ExistingMultiOriginDistId"}], "/CloudFrontImportStack/ExistingS3WebsiteDistId": [{"type": "aws:cdk:logicalId", "data": "ExistingS3WebsiteDistId"}], "/CloudFrontImportStack/ExistingCertificateArn": [{"type": "aws:cdk:logicalId", "data": "ExistingCertificateArn"}], "/CloudFrontImportStack/ExistingS3BucketName": [{"type": "aws:cdk:logicalId", "data": "ExistingS3BucketName"}], "/CloudFrontImportStack/TerraformMigrationStatus": [{"type": "aws:cdk:analytics:construct", "data": {"parameterName": "*", "stringValue": "*", "description": "*"}}], "/CloudFrontImportStack/TerraformMigrationStatus/Resource": [{"type": "aws:cdk:logicalId", "data": "TerraformMigrationStatus24AF15F5"}], "/CloudFrontImportStack/DistributionInventory": [{"type": "aws:cdk:analytics:construct", "data": {"parameterName": "*", "stringValue": "*", "description": "*"}}], "/CloudFrontImportStack/DistributionInventory/Resource": [{"type": "aws:cdk:logicalId", "data": "DistributionInventoryB0010989"}], "/CloudFrontImportStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/CloudFrontImportStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/CloudFrontImportStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "CloudFrontImportStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1018.1"}