import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
/**
 * Stack that imports existing CloudFront distributions from Terraform-managed infrastructure
 * This represents the current state without creating new resources - for migration purposes
 */
export declare class CloudFrontImportStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props?: cdk.StackProps);
    private importExistingDistributions;
    private addOutputs;
    private createConfigurationDocumentation;
}
