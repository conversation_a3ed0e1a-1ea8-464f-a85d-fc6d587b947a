# Terraform to CDK Migration Guide

This guide helps you migrate your CloudFront distributions from Terraform to AWS CDK using the AWS Labs CDK MCP server.

## Overview

You have 7 existing CloudFront distributions managed by Terraform. This CDK project imports these existing resources without creating new ones, allowing for a safe migration path.

## Current Setup

### AWS Labs CDK MCP Configuration
Your `.vscode/mcp.json` is configured with:
```json
{
  "servers": {
    "awslabs.cdk-mcp-server": {
      "command": "uvx",
      "args": ["awslabs.cdk-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR"
      }
    }
  }
}
```

### Project Structure
```
├── bin/awsmcpdemo.ts                    # CDK app entry point
├── lib/cloudfront-import-stack.ts       # Import existing distributions
├── docs/existing-cloudfront-configuration.md # Current state documentation
├── package.json                         # Dependencies
└── .vscode/mcp.json                     # MCP server configuration
```

## Migration Strategy

### Phase 1: Import Existing Resources (Current)
✅ **Status**: Complete
- Import all 7 CloudFront distributions using `Distribution.fromDistributionAttributes()`
- Import ACM certificate and S3 bucket references
- Document current configuration
- No new resources created

### Phase 2: Validate Imports
```bash
# Build and synthesize to verify imports
npm run build
npx cdk synth --profile AWS-dev-Delty-Admin-902537659456

# Deploy import stack (creates no new resources)
npx cdk deploy CloudFrontImportStack --profile AWS-dev-Delty-Admin-902537659456
```

### Phase 3: Gradual Migration
1. **Start with simplest distribution** (e.g., EXZC66UKJAABK)
2. **Create new CDK-managed distribution** alongside existing one
3. **Test thoroughly** before switching traffic
4. **Update DNS/references** to point to new distribution
5. **Remove from Terraform** after validation

### Phase 4: Complex Distributions
- Handle ES27SJ7X3UX1M (complex multi-origin) last
- Ensure all cache behaviors are replicated
- Validate custom domain and certificate

## Using AWS Labs CDK MCP

The AWS Labs CDK MCP server provides enhanced CDK capabilities. Here's how to leverage it:

### 1. Resource Discovery
The MCP server can help discover and analyze your existing AWS resources:
```typescript
// The import stack already references your distributions
// Use MCP tools to analyze configurations
```

### 2. Configuration Validation
Use the MCP server to validate your CDK configurations match existing resources:
```bash
# Synthesize and compare with existing resources
npx cdk synth --profile AWS-dev-Delty-Admin-902537659456
```

### 3. Deployment Safety
The import approach ensures no accidental resource creation:
- All distributions are imported, not created
- Terraform state remains intact
- No service interruption

## Commands for Migration

### Initial Setup
```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Verify configuration
npx cdk synth --profile AWS-dev-Delty-Admin-902537659456
```

### Deploy Import Stack
```bash
# Deploy without creating new resources
npx cdk deploy CloudFrontImportStack --profile AWS-dev-Delty-Admin-902537659456
```

### Verify Imports
```bash
# Check stack outputs
aws cloudformation describe-stacks \
  --stack-name CloudFrontImportStack \
  --profile AWS-dev-Delty-Admin-902537659456 \
  --query 'Stacks[0].Outputs'
```

## Safety Measures

### 1. No Resource Creation
- All distributions use `fromDistributionAttributes()` for imports
- No `new Distribution()` calls that would create resources
- Terraform state remains authoritative

### 2. Configuration Documentation
- Complete configuration captured in `docs/existing-cloudfront-configuration.md`
- SSM parameters track migration status
- All settings documented for reference

### 3. Rollback Plan
- Keep Terraform state files
- CDK import stack can be deleted without affecting distributions
- Original Terraform can still manage resources

## Validation Checklist

Before proceeding with actual migration:

- [ ] All 7 distributions imported successfully
- [ ] CDK synth produces valid CloudFormation
- [ ] No new resources in synthesized templates
- [ ] All distribution IDs match existing ones
- [ ] Certificate and S3 bucket references correct
- [ ] SSM parameters created for tracking

## Next Steps

1. **Deploy Import Stack**: Establishes CDK references to existing resources
2. **Create Migration Plan**: Decide order for migrating individual distributions
3. **Test Environment**: Set up testing for new CDK-managed distributions
4. **Gradual Migration**: Move distributions one by one
5. **Cleanup**: Remove Terraform state after successful migration

## Troubleshooting

### Common Issues
1. **Distribution not found**: Verify distribution ID and region
2. **Certificate import fails**: Check certificate ARN and region (must be us-east-1)
3. **S3 bucket access**: Ensure bucket exists and is accessible

### MCP Server Issues
```bash
# Check MCP server status
uvx awslabs.cdk-mcp-server@latest --help

# Restart VS Code if MCP connection issues
```

### AWS Profile Issues
```bash
# Verify AWS profile
aws sts get-caller-identity --profile AWS-dev-Delty-Admin-902537659456

# Refresh SSO if needed
aws sso login --profile AWS-dev-Delty-Admin-902537659456
```

## Benefits of This Approach

1. **Zero Downtime**: No service interruption during migration
2. **Safe Migration**: Import existing resources without changes
3. **Gradual Process**: Migrate distributions one at a time
4. **Rollback Capability**: Can revert to Terraform if needed
5. **Documentation**: Complete configuration capture for reference

This approach ensures a safe, controlled migration from Terraform to CDK while preserving all your existing CloudFront configurations.
