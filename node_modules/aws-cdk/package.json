{"name": "aws-cdk", "description": "AWS CDK CLI, the command line tool for CDK apps", "repository": {"type": "git", "url": "https://github.com/aws/aws-cdk-cli", "directory": "packages/aws-cdk"}, "bin": {"cdk": "bin/cdk"}, "scripts": {"build": "npx projen build", "bump": "npx projen bump", "check-for-updates": "npx projen check-for-updates", "check-licenses": "npx projen check-licenses", "compile": "npx projen compile", "default": "npx projen default", "eslint": "npx projen eslint", "gather-versions": "npx projen gather-versions", "nx": "npx projen nx", "package": "npx projen package", "post-compile": "npx projen post-compile", "pre-compile": "npx projen pre-compile", "test": "npx projen test", "test:watch": "npx projen test:watch", "unbump": "npx projen unbump", "watch": "npx projen watch", "projen": "npx projen"}, "author": {"name": "Amazon Web Services", "url": "https://aws.amazon.com", "organization": true}, "devDependencies": {"@aws-cdk/cli-plugin-contract": "2.181.0", "@aws-cdk/node-bundle": "0.0.0", "@aws-cdk/user-input-gen": "0.0.0", "@cdklabs/eslint-plugin": "^1.3.2", "@stylistic/eslint-plugin": "^3", "@types/archiver": "^6.0.3", "@types/fs-extra": "^9", "@types/jest": "^29.5.14", "@types/mockery": "^1.4.33", "@types/node": "^16", "@types/promptly": "^3.0.5", "@types/semver": "^7.7.0", "@types/sinon": "^17.0.4", "@types/yargs": "^15", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "aws-cdk-lib": "^2.200.1", "aws-sdk-client-mock": "^4.1.0", "aws-sdk-client-mock-jest": "^4.1.0", "axios": "^1.9.0", "commit-and-tag-version": "^12", "constructs": "^10.0.0", "eslint": "^9", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.0", "eslint-plugin-jsdoc": "^50.7.1", "eslint-plugin-prettier": "^5.4.1", "fast-check": "^3.23.2", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16", "jest-mock": "^29.7.0", "license-checker": "^25.0.1", "madge": "^8.0.0", "nock": "13", "prettier": "^2.8", "sinon": "^19.0.5", "ts-jest": "^29.3.4", "ts-mock-imports": "^1.3.17", "typescript": "5.6", "xml-js": "^1.6.11", "@aws-cdk/cloud-assembly-schema": ">=44.4.0", "@aws-cdk/cloudformation-diff": "2.182.0", "@aws-cdk/cx-api": "^2.200.1", "@aws-cdk/toolkit-lib": "^1.1.1", "@aws-sdk/client-appsync": "^3", "@aws-sdk/client-cloudcontrol": "^3", "@aws-sdk/client-cloudformation": "^3", "@aws-sdk/client-cloudwatch-logs": "^3", "@aws-sdk/client-codebuild": "^3", "@aws-sdk/client-ec2": "^3", "@aws-sdk/client-ecr": "^3", "@aws-sdk/client-ecs": "^3", "@aws-sdk/client-elastic-load-balancing-v2": "^3", "@aws-sdk/client-iam": "^3", "@aws-sdk/client-kms": "^3", "@aws-sdk/client-lambda": "^3", "@aws-sdk/client-route-53": "^3", "@aws-sdk/client-s3": "^3", "@aws-sdk/client-secrets-manager": "^3", "@aws-sdk/client-sfn": "^3", "@aws-sdk/client-ssm": "^3", "@aws-sdk/client-sts": "^3", "@aws-sdk/credential-providers": "^3", "@aws-sdk/ec2-metadata-service": "^3", "@aws-sdk/lib-storage": "^3", "@aws-sdk/middleware-endpoint": "^3.374.0", "@aws-sdk/util-retry": "^3.374.0", "@aws-sdk/util-waiter": "^3.374.0", "@smithy/middleware-endpoint": "^4.1.11", "@smithy/property-provider": "^4.0.4", "@smithy/shared-ini-file-loader": "^4.0.4", "@smithy/types": "^4.3.1", "@smithy/util-retry": "^4.0.5", "@smithy/util-waiter": "^4.0.5", "archiver": "^7.0.1", "camelcase": "^6", "cdk-assets": "^3.3.0", "cdk-from-cfn": "^0.220.0", "chalk": "^4", "chokidar": "^3", "decamelize": "^5", "fs-extra": "^9", "glob": "^11.0.2", "minimatch": "10.0.1", "p-limit": "^3", "promptly": "^3.2.0", "proxy-agent": "^6.5.0", "semver": "^7.7.2", "strip-ansi": "^6", "uuid": "^11.1.0", "wrap-ansi": "^7", "yaml": "^1", "yargs": "^15"}, "dependencies": {}, "keywords": ["aws", "cdk"], "engines": {"node": ">= 18.0.0"}, "main": "lib/index.js", "license": "Apache-2.0", "homepage": "https://github.com/aws/aws-cdk", "publishConfig": {"access": "public"}, "version": "2.1018.1", "types": "lib/index.d.ts", "exports": {"./package.json": "./package.json", "./build-info.json": "./build-info.json", ".": "./lib/legacy-exports.js", "./bin/cdk": "./bin/cdk", "./lib/api/bootstrap/bootstrap-template.yaml": "./lib/api/bootstrap/bootstrap-template.yaml", "./lib/util": "./lib/legacy-exports.js", "./lib": "./lib/legacy-exports.js", "./lib/api/plugin": "./lib/legacy-exports.js", "./lib/util/content-hash": "./lib/legacy-exports.js", "./lib/settings": "./lib/legacy-exports.js", "./lib/api/bootstrap": "./lib/legacy-exports.js", "./lib/api/cxapp/cloud-assembly": "./lib/legacy-exports.js", "./lib/api/cxapp/cloud-executable": "./lib/legacy-exports.js", "./lib/api/cxapp/exec": "./lib/legacy-exports.js", "./lib/diff": "./lib/legacy-exports.js", "./lib/api/util/string-manipulation": "./lib/legacy-exports.js", "./lib/util/console-formatters": "./lib/legacy-exports.js", "./lib/util/tracing": "./lib/legacy-exports.js", "./lib/commands/docs": "./lib/legacy-exports.js", "./lib/api/hotswap/common": "./lib/legacy-exports.js", "./lib/util/objects": "./lib/legacy-exports.js", "./lib/api/deployments": "./lib/legacy-exports.js", "./lib/util/directories": "./lib/legacy-exports.js", "./lib/version": "./lib/legacy-exports.js", "./lib/init": "./lib/legacy-exports.js", "./lib/api/aws-auth/cached": "./lib/legacy-exports.js", "./lib/api/deploy-stack": "./lib/legacy-exports.js", "./lib/api/evaluate-cloudformation-template": "./lib/legacy-exports.js", "./lib/api/aws-auth/credential-plugins": "./lib/legacy-exports.js", "./lib/api/aws-auth/awscli-compatible": "./lib/legacy-exports.js", "./lib/notices": "./lib/legacy-exports.js", "./lib/index": "./lib/legacy-exports.js", "./lib/api/aws-auth/index.js": "./lib/legacy-exports.js", "./lib/api/aws-auth": "./lib/legacy-exports.js", "./lib/logging": "./lib/legacy-exports.js"}, "//": "~~ Generated by projen. To modify, edit .projenrc.js and run \"npx projen\".", "optionalDependencies": {"fsevents": "2.3.2"}}