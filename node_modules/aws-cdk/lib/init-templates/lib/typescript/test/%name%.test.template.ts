// import * as cdk from 'aws-cdk-lib';
// import { Template } from 'aws-cdk-lib/assertions';
// import * as %name.PascalCased% from '../lib/index';

// example test. To run these tests, uncomment this file along with the
// example resource in lib/index.ts
test('SQS Queue Created', () => {
//   const app = new cdk.App();
//   const stack = new cdk.Stack(app, "TestStack");
//   // WHEN
//   new %name.PascalCased%.%name.PascalCased%(stack, 'MyTestConstruct');
//   // THEN
//   const template = Template.fromStack(stack);

//   template.hasResourceProperties('AWS::SQS::Queue', {
//     VisibilityTimeout: 300
//   });
});
