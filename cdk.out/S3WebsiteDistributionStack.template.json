{"Description": "CloudFront distributions for S3 websites and external origins", "Resources": {"CloudFrontWebACL": {"Type": "AWS::WAFv2::WebACL", "Properties": {"DefaultAction": {"Allow": {}}, "Description": "Web ACL for Mikes Website CloudFront Distribution", "Name": "CreatedByCloudFront-MikesWebsite-WebACL", "Rules": [{"Name": "AWSManagedRulesCommonRuleSet", "OverrideAction": {"None": {}}, "Priority": 1, "Statement": {"ManagedRuleGroupStatement": {"Name": "AWSManagedRulesCommonRuleSet", "VendorName": "AWS"}}, "VisibilityConfig": {"CloudWatchMetricsEnabled": true, "MetricName": "CommonRuleSetMetric", "SampledRequestsEnabled": true}}, {"Name": "AWSManagedRulesKnownBadInputsRuleSet", "OverrideAction": {"None": {}}, "Priority": 2, "Statement": {"ManagedRuleGroupStatement": {"Name": "AWSManagedRulesKnownBadInputsRuleSet", "VendorName": "AWS"}}, "VisibilityConfig": {"CloudWatchMetricsEnabled": true, "MetricName": "KnownBadInputsRuleSetMetric", "SampledRequestsEnabled": true}}], "Scope": "CLOUDFRONT", "VisibilityConfig": {"CloudWatchMetricsEnabled": true, "MetricName": "MikesWebsiteWebACL", "SampledRequestsEnabled": true}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/CloudFrontWebACL"}}, "S3WebsiteDistributionEFFC0A69": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "Comment": "S3 Website Distribution - E1YNWVHTAGY30T Recreation", "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "CachedMethods": ["GET", "HEAD"], "Compress": true, "TargetOriginId": "S3WebsiteDistributionStackS3WebsiteDistributionOrigin176381268", "ViewerProtocolPolicy": "https-only"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "http-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "mikeswebsite.s3-website-us-east-1.amazonaws.com", "Id": "S3WebsiteDistributionStackS3WebsiteDistributionOrigin176381268"}], "PriceClass": "PriceClass_All", "WebACLId": {"Fn::GetAtt": ["CloudFrontWebACL", "<PERSON><PERSON>"]}}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/S3WebsiteDistribution/Resource"}}, "ExternalWebsiteCache81929B10": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Custom cache policy for external website", "DefaultTTL": 86400, "MaxTTL": 86400, "MinTTL": 0, "Name": "ExternalWebsiteCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "none"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "none"}}}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/ExternalWebsiteCache/Resource"}}, "ExternalWebsiteDistribution39A9CF4A": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "Comment": "External Website Distribution - EXZC66UKJAABK Recreation", "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD"], "CachePolicyId": {"Ref": "ExternalWebsiteCache81929B10"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "TargetOriginId": "S3WebsiteDistributionStackExternalWebsiteDistributionOrigin1FAA4C476", "ViewerProtocolPolicy": "allow-all"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "quotewizard.com", "Id": "S3WebsiteDistributionStackExternalWebsiteDistributionOrigin1FAA4C476"}], "PriceClass": "PriceClass_All"}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/ExternalWebsiteDistribution/Resource"}}, "MultiOriginCache2868F312": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Custom cache policy for multi-origin distribution", "DefaultTTL": 86400, "MaxTTL": 31536000, "MinTTL": 0, "Name": "MultiOriginCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "all"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "all"}}}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/MultiOriginCache/Resource"}}, "MultiOriginDistribution39008539": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "Comment": "Multi-Origin Distribution - E10NZFW9D6AMU2 Recreation", "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "MultiOriginCache2868F312"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "TargetOriginId": "S3WebsiteDistributionStackMultiOriginDistributionOrigin14C00D240", "ViewerProtocolPolicy": "redirect-to-https"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "cbth.quotewizard.com", "Id": "S3WebsiteDistributionStackMultiOriginDistributionOrigin14C00D240"}], "PriceClass": "PriceClass_All"}}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/MultiOriginDistribution/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/92TwW7UMBCGn6U+Vo7ZBolDbku4VCqwahEcKlQ59qSZruMJ9iRhFe27o3XTzVJuPQDqyfpnxv+MP9u5ylcrdXGmx5gZu80cVmq6YW22Uo/xbhp1PeSqrP03qNbllTSOelsH8qymDxg5YNUzkr+dhKG2Bc+iEOdCCvC6cmBFwaEHKRrm7iuEiORFkVQupOgCGiidjlEUYnMUd2vnjhaX3fDuyWWEam3cpZ17WKh17/g9NHpACqKYBAW8Rz/nB4QRwiYQkyG3IYdmN3ePGXm3E1Jo52gE+xG4IRvnjUab5o8YtV2Aw6SPs6Sao+e52EthIDDWaDTD04TUavSfdAtxLvornF4IJrHIdDJ9AZdau/g6wQSwGMBwxpSl1/MPAH2XZe1Pv5wsF5/D91vUYdfS/ITpfPwv7OZAq3+eCPSLaEBbCAuoFPzRQ9jdcEB//yxjiLYIvwXTlf53QyWMJ+T2e3kNkfpg4DblPvfc9byXniyoh/hmyHN18Vatzh4iYhZ6z9iCun5cfwGaLmEKPwUAAA=="}, "Metadata": {"aws:cdk:path": "S3WebsiteDistributionStack/CDKMetadata/Default"}}}, "Outputs": {"S3WebsiteDistributionDomainName": {"Description": "Domain name for S3 Website Distribution", "Value": {"Fn::GetAtt": ["S3WebsiteDistributionEFFC0A69", "DomainName"]}}, "ExternalWebsiteDistributionDomainName": {"Description": "Domain name for External Website Distribution", "Value": {"Fn::GetAtt": ["ExternalWebsiteDistribution39A9CF4A", "DomainName"]}}, "MultiOriginDistributionDomainName": {"Description": "Domain name for Multi-Origin Distribution", "Value": {"Fn::GetAtt": ["MultiOriginDistribution39008539", "DomainName"]}}, "WebACLArn": {"Description": "ARN of the WAF Web ACL", "Value": {"Fn::GetAtt": ["CloudFrontWebACL", "<PERSON><PERSON>"]}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}