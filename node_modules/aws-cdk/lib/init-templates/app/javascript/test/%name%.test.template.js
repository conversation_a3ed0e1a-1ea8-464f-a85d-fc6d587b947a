// const cdk = require('aws-cdk-lib');
// const { Template } = require('aws-cdk-lib/assertions');
// const %name.PascalCased% = require('../lib/%name%-stack');

// example test. To run these tests, uncomment this file along with the
// example resource in lib/%name%-stack.js
test('SQS Queue Created', () => {
//   const app = new cdk.App();
//   // WHEN
//   const stack = new %name.PascalCased%.%name.PascalCased%Stack(app, 'MyTestStack');
//   // THEN
//   const template = Template.fromStack(stack);

//   template.hasResourceProperties('AWS::SQS::Queue', {
//     VisibilityTimeout: 300
//   });
});
