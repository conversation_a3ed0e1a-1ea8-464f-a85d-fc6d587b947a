"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3WebsiteDistributionStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront = __importStar(require("aws-cdk-lib/aws-cloudfront"));
const origins = __importStar(require("aws-cdk-lib/aws-cloudfront-origins"));
const wafv2 = __importStar(require("aws-cdk-lib/aws-wafv2"));
class S3WebsiteDistributionStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Create a WAF Web ACL (recreating the existing one)
        const webAcl = new wafv2.CfnWebACL(this, 'CloudFrontWebACL', {
            scope: 'CLOUDFRONT',
            defaultAction: { allow: {} },
            name: 'CreatedByCloudFront-MikesWebsite-WebACL',
            description: 'Web ACL for Mikes Website CloudFront Distribution',
            rules: [
                {
                    name: 'AWSManagedRulesCommonRuleSet',
                    priority: 1,
                    overrideAction: { none: {} },
                    statement: {
                        managedRuleGroupStatement: {
                            vendorName: 'AWS',
                            name: 'AWSManagedRulesCommonRuleSet',
                        },
                    },
                    visibilityConfig: {
                        sampledRequestsEnabled: true,
                        cloudWatchMetricsEnabled: true,
                        metricName: 'CommonRuleSetMetric',
                    },
                },
                {
                    name: 'AWSManagedRulesKnownBadInputsRuleSet',
                    priority: 2,
                    overrideAction: { none: {} },
                    statement: {
                        managedRuleGroupStatement: {
                            vendorName: 'AWS',
                            name: 'AWSManagedRulesKnownBadInputsRuleSet',
                        },
                    },
                    visibilityConfig: {
                        sampledRequestsEnabled: true,
                        cloudWatchMetricsEnabled: true,
                        metricName: 'KnownBadInputsRuleSetMetric',
                    },
                },
            ],
            visibilityConfig: {
                sampledRequestsEnabled: true,
                cloudWatchMetricsEnabled: true,
                metricName: 'MikesWebsiteWebACL',
            },
        });
        // Distribution E1YNWVHTAGY30T - S3 Website with WAF
        const s3WebsiteDistribution = new cloudfront.Distribution(this, 'S3WebsiteDistribution', {
            comment: 'S3 Website Distribution - E1YNWVHTAGY30T Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: true,
            webAclId: webAcl.attrArn,
            defaultBehavior: {
                origin: new origins.HttpOrigin('mikeswebsite.s3-website-us-east-1.amazonaws.com', {
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: true,
                // Using the managed caching optimized cache policy
                cachePolicy: cloudfront.CachePolicy.fromCachePolicyId(this, 'CachingOptimized', '658327ea-f89d-4fab-a63d-7e88639e58f6'),
            },
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Distribution EXZC66UKJAABK - External website distribution
        const externalWebsiteDistribution = new cloudfront.Distribution(this, 'ExternalWebsiteDistribution', {
            comment: 'External Website Distribution - EXZC66UKJAABK Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: true,
            defaultBehavior: {
                origin: new origins.HttpOrigin('quotewizard.com', {
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: false,
                cachePolicy: new cloudfront.CachePolicy(this, 'ExternalWebsiteCache', {
                    cachePolicyName: 'ExternalWebsiteCachePolicy',
                    comment: 'Custom cache policy for external website',
                    defaultTtl: cdk.Duration.seconds(86400),
                    maxTtl: cdk.Duration.seconds(86400),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.none(),
                }),
            },
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Distribution E10NZFW9D6AMU2 - Multi-origin distribution
        const multiOriginDistribution = new cloudfront.Distribution(this, 'MultiOriginDistribution', {
            comment: 'Multi-Origin Distribution - E10NZFW9D6AMU2 Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: true,
            defaultBehavior: {
                origin: new origins.HttpOrigin('cbth.quotewizard.com', {
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: false,
                cachePolicy: new cloudfront.CachePolicy(this, 'MultiOriginCache', {
                    cachePolicyName: 'MultiOriginCachePolicy',
                    comment: 'Custom cache policy for multi-origin distribution',
                    defaultTtl: cdk.Duration.seconds(86400),
                    maxTtl: cdk.Duration.seconds(31536000),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.all(),
                }),
            },
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Output the distribution domain names
        new cdk.CfnOutput(this, 'S3WebsiteDistributionDomainName', {
            value: s3WebsiteDistribution.distributionDomainName,
            description: 'Domain name for S3 Website Distribution',
        });
        new cdk.CfnOutput(this, 'ExternalWebsiteDistributionDomainName', {
            value: externalWebsiteDistribution.distributionDomainName,
            description: 'Domain name for External Website Distribution',
        });
        new cdk.CfnOutput(this, 'MultiOriginDistributionDomainName', {
            value: multiOriginDistribution.distributionDomainName,
            description: 'Domain name for Multi-Origin Distribution',
        });
        new cdk.CfnOutput(this, 'WebACLArn', {
            value: webAcl.attrArn,
            description: 'ARN of the WAF Web ACL',
        });
    }
}
exports.S3WebsiteDistributionStack = S3WebsiteDistributionStack;
//# sourceMappingURL=data:application/json;base64,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