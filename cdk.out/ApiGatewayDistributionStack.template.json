{"Description": "CloudFront distributions for API Gateway origins", "Resources": {"ApiGatewayCache11DB2D25A": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Custom cache policy for API Gateway distribution 1", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "ApiGatewayCustomCachePolicy1", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "whitelist", "Cookies": ["Accept", "Origin", "<PERSON><PERSON><PERSON>"]}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "whitelist", "Headers": ["*"]}, "QueryStringsConfig": {"QueryStringBehavior": "none"}}}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayCache1/Resource"}}, "ApiGatewayDistribution1E054F055": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "Comment": "API Gateway Distribution - E1Y2GZXHXCGI8H Recreation", "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "ApiGatewayCache11DB2D25A"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "TargetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution1Origin1917ECBC9", "ViewerProtocolPolicy": "https-only"}, "Enabled": true, "HttpVersion": "http1.1", "IPV6Enabled": false, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "p0z7suimt8.execute-api.us-east-1.amazonaws.com", "Id": "ApiGatewayDistributionStackApiGatewayDistribution1Origin1917ECBC9", "OriginPath": "/dev"}], "PriceClass": "PriceClass_All"}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayDistribution1/Resource"}}, "ApiGatewayCache2A091AD93": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Custom cache policy for API Gateway distribution 2", "DefaultTTL": 0, "MaxTTL": 0, "MinTTL": 0, "Name": "ApiGatewayCustomCachePolicy2", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "none"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "whitelist", "Headers": ["CloudFront-Is-Tablet-Viewer", "CloudFront-Is-Mobile-Viewer", "CloudFront-Is-SmartTV-Viewer", "CloudFront-Is-Desktop-Viewer"]}, "QueryStringsConfig": {"QueryStringBehavior": "none"}}}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayCache2/Resource"}}, "JpgCachePolicy8CC17EDB": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Cache policy for JPG files", "DefaultTTL": 86400, "MaxTTL": 31536000, "MinTTL": 0, "Name": "JpgFilesCachePolicy", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "none"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "none"}, "QueryStringsConfig": {"QueryStringBehavior": "none"}}}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/JpgCachePolicy/Resource"}}, "ApiGatewayDistribution2D19290A1": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "CacheBehaviors": [{"AllowedMethods": ["GET", "HEAD"], "CachePolicyId": {"Ref": "JpgCachePolicy8CC17EDB"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "PathPattern": "Dir*/*.jpg", "TargetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution2Origin26D325D9E", "ViewerProtocolPolicy": "allow-all"}], "Comment": "API Gateway Distribution with Cache Behaviors - E131RZ69QYLX7O Recreation", "CustomErrorResponses": [{"ErrorCachingMinTTL": 0, "ErrorCode": 403}, {"ErrorCachingMinTTL": 0, "ErrorCode": 404}, {"ErrorCachingMinTTL": 0, "ErrorCode": 502}, {"ErrorCachingMinTTL": 0, "ErrorCode": 504}], "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": {"Ref": "ApiGatewayCache2A091AD93"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "TargetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution2Origin14B834861", "ViewerProtocolPolicy": "https-only"}, "Enabled": true, "HttpVersion": "http1.1", "IPV6Enabled": false, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "20rhz2v7tc.execute-api.us-west-2.amazonaws.com", "Id": "ApiGatewayDistributionStackApiGatewayDistribution2Origin14B834861", "OriginPath": "/prod/regionTest"}, {"CustomOriginConfig": {"OriginProtocolPolicy": "https-only", "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "20rhz2v7tc.execute-api.us-west-2.amazonaws.com", "Id": "ApiGatewayDistributionStackApiGatewayDistribution2Origin26D325D9E", "OriginPath": "/prod/regionTest"}], "PriceClass": "PriceClass_All"}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayDistribution2/Resource"}}, "ApiGatewayCache31542FF1E": {"Type": "AWS::CloudFront::CachePolicy", "Properties": {"CachePolicyConfig": {"Comment": "Custom cache policy for API Gateway distribution 3", "DefaultTTL": 86400, "MaxTTL": 31536000, "MinTTL": 0, "Name": "ApiGatewayCustomCachePolicy3", "ParametersInCacheKeyAndForwardedToOrigin": {"CookiesConfig": {"CookieBehavior": "none"}, "EnableAcceptEncodingBrotli": false, "EnableAcceptEncodingGzip": false, "HeadersConfig": {"HeaderBehavior": "whitelist", "Headers": ["CloudFront-Viewer-Country", "CloudFront-Forwarded-Proto", "CloudFront-Is-Tablet-Viewer", "CloudFront-Is-Mobile-Viewer", "User-Agent", "CloudFront-Is-SmartTV-Viewer", "CloudFront-Is-Desktop-Viewer"]}, "QueryStringsConfig": {"QueryStringBehavior": "none"}}}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayCache3/Resource"}}, "ApiGatewayDistribution3376E283A": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"Aliases": [], "Comment": "API Gateway Distribution with Device Detection - E2EZONTUD8DA1O Recreation", "DefaultCacheBehavior": {"AllowedMethods": ["GET", "HEAD"], "CachePolicyId": {"Ref": "ApiGatewayCache31542FF1E"}, "CachedMethods": ["GET", "HEAD"], "Compress": false, "TargetOriginId": "ApiGatewayDistributionStackApiGatewayDistribution3Origin1A7666033", "ViewerProtocolPolicy": "allow-all"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"ConnectionAttempts": 3, "ConnectionTimeout": 10, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginKeepaliveTimeout": 5, "OriginProtocolPolicy": "https-only", "OriginReadTimeout": 30, "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": "9uerlzte0e.execute-api.us-east-1.amazonaws.com", "Id": "ApiGatewayDistributionStackApiGatewayDistribution3Origin1A7666033", "OriginPath": "/test/testedge"}], "PriceClass": "PriceClass_All"}}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/ApiGatewayDistribution3/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/+1UQW7bMBB8S3gsJNVWgR50a91LDm2NuOjFMII1uYo2prgqubJrCP57QVuynAAF0qA5FOlJmCFFzs4MmGf5ZJJNr2AXUm02qaV11i0E9CaBXbjVlltTenaSdTPQFc7Zkt4vO6VH9AVqVIV6oxKlua7RSY8MltBa+Sa2J2r4eQHIjaBCMOg/YgVbYt+TP1r0+4V4cnePVjTzhvABeUj+i/qXRa2SWekuOpZ8oiCe1q0Qu1i4B9rQwdqiUYX4FhNViTTf0Qdip4ojmmZTlajGk8aZhRBUoeZncPvB2vMh1832vSpKsAHPM4/SOsWe7sj1924Jd+jnnoU125PS/saQsrN7lSiwlndoPqNUbMIwcpzsMcd14zFq62+/SKVPSmn0QiVpkCEkwzWQi6mFMc7XaQ4YQ7EeYAdNw9/oPfsbDA27EH1adkcZCwFphz3S1zca+BKLq6emB8aMni7jti7Sv7Hz6GAKx5D+opurZ/Qo/6MWnU55ZoleaOonRXR8mi6fo0MkvrbStHJIHBvM7sPbbZ5n03fZ5Oo+EKW+dUI1Zjen7y83YlTGaAcAAA=="}, "Metadata": {"aws:cdk:path": "ApiGatewayDistributionStack/CDKMetadata/Default"}}}, "Outputs": {"ApiGatewayDistribution1DomainName": {"Description": "Domain name for API Gateway Distribution 1", "Value": {"Fn::GetAtt": ["ApiGatewayDistribution1E054F055", "DomainName"]}}, "ApiGatewayDistribution2DomainName": {"Description": "Domain name for API Gateway Distribution 2", "Value": {"Fn::GetAtt": ["ApiGatewayDistribution2D19290A1", "DomainName"]}}, "ApiGatewayDistribution3DomainName": {"Description": "Domain name for API Gateway Distribution 3", "Value": {"Fn::GetAtt": ["ApiGatewayDistribution3376E283A", "DomainName"]}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}