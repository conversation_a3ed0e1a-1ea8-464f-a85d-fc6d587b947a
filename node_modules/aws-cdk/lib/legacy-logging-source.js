"use strict";
// This is an exact copy of the file `packages/aws-cdk/lib/logging.ts` from 2024-11-29
// https://github.com/aws/aws-cdk/blob/81cde0e2e1f83f80273d14724d5518cc20dc5a80/packages/aws-cdk/lib/logging.ts
// After this we started refactoring the file and functionality changed significantly.
// In order to preserver backwards-compatibly for users with unsanctioned usage of this file,
// we keep a copy of the original version around.
// See https://github.com/aws/aws-cdk/pull/33021 for more information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.data = exports.print = exports.highlight = exports.success = exports.warning = exports.error = exports.debug = exports.trace = exports.CI = exports.logLevel = exports.LogLevel = void 0;
exports.withCorkedLogging = withCorkedLogging;
exports.setLogLevel = setLogLevel;
exports.setCI = setCI;
exports.increaseVerbosity = increaseVerbosity;
exports.prefix = prefix;
const util = require("util");
const chalk = require("chalk");
const { stdout, stderr } = process;
async function withCorkedLogging(block) {
    corkLogging();
    try {
        return await block();
    }
    finally {
        uncorkLogging();
    }
}
let CORK_COUNTER = 0;
const logBuffer = [];
function corked() {
    return CORK_COUNTER !== 0;
}
function corkLogging() {
    CORK_COUNTER += 1;
}
function uncorkLogging() {
    CORK_COUNTER -= 1;
    if (!corked()) {
        logBuffer.forEach(([stream, str]) => stream.write(str + '\n'));
        logBuffer.splice(0);
    }
}
const logger = (stream, styles, timestamp) => (fmt, ...args) => {
    const ts = timestamp ? `[${formatTime(new Date())}] ` : '';
    let str = ts + util.format(fmt, ...args);
    if (styles && styles.length) {
        str = styles.reduce((a, style) => style(a), str);
    }
    const realStream = typeof stream === 'function' ? stream() : stream;
    // Logger is currently corked, so we store the message to be printed
    // later when we are uncorked.
    if (corked()) {
        logBuffer.push([realStream, str]);
        return;
    }
    realStream.write(str + '\n');
};
function formatTime(d) {
    return `${lpad(d.getHours(), 2)}:${lpad(d.getMinutes(), 2)}:${lpad(d.getSeconds(), 2)}`;
    function lpad(x, w) {
        const s = `${x}`;
        return '0'.repeat(Math.max(w - s.length, 0)) + s;
    }
}
var LogLevel;
(function (LogLevel) {
    /** Not verbose at all */
    LogLevel[LogLevel["DEFAULT"] = 0] = "DEFAULT";
    /** Pretty verbose */
    LogLevel[LogLevel["DEBUG"] = 1] = "DEBUG";
    /** Extremely verbose */
    LogLevel[LogLevel["TRACE"] = 2] = "TRACE";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
exports.logLevel = LogLevel.DEFAULT;
exports.CI = false;
function setLogLevel(newLogLevel) {
    exports.logLevel = newLogLevel;
}
function setCI(newCI) {
    exports.CI = newCI;
}
function increaseVerbosity() {
    exports.logLevel += 1;
}
const stream = () => exports.CI ? stdout : stderr;
const _debug = logger(stream, [chalk.gray], true);
const trace = (fmt, ...args) => exports.logLevel >= LogLevel.TRACE && _debug(fmt, ...args);
exports.trace = trace;
const debug = (fmt, ...args) => exports.logLevel >= LogLevel.DEBUG && _debug(fmt, ...args);
exports.debug = debug;
exports.error = logger(stderr, [chalk.red]);
exports.warning = logger(stream, [chalk.yellow]);
exports.success = logger(stream, [chalk.green]);
exports.highlight = logger(stream, [chalk.bold]);
exports.print = logger(stream);
exports.data = logger(stdout);
/**
 * Create a logger output that features a constant prefix string.
 *
 * @param prefixString - the prefix string to be appended before any log entry.
 * @param fn   - the logger function to be used (typically one of the other functions in this module)
 *
 * @returns a new LoggerFunction.
 */
function prefix(prefixString, fn) {
    return (fmt, ...args) => fn(`%s ${fmt}`, prefixString, ...args);
}
//# sourceMappingURL=data:application/json;base64,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