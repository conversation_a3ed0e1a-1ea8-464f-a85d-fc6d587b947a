"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aliases = exports.describe = exports.command = void 0;
exports.docs = docs;
const childProcess = require("child_process");
const chalk = require("chalk");
const logging_1 = require("../../lib/logging");
exports.command = 'docs';
exports.describe = 'Opens the reference documentation in a browser';
exports.aliases = ['doc'];
async function docs(options) {
    const url = 'https://docs.aws.amazon.com/cdk/api/v2/';
    (0, logging_1.info)(chalk.green(url));
    const browserCommand = (options.browser).replace(/%u/g, url);
    (0, logging_1.debug)(`Opening documentation ${chalk.green(browserCommand)}`);
    return new Promise((resolve, _reject) => {
        childProcess.exec(browserCommand, (err, stdout, stderr) => {
            if (err) {
                (0, logging_1.debug)(`An error occurred when trying to open a browser: ${err.stack || err.message}`);
                return resolve(0);
            }
            if (stdout) {
                (0, logging_1.debug)(stdout);
            }
            if (stderr) {
                (0, logging_1.warning)(stderr);
            }
            resolve(0);
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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