#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront_import_stack_1 = require("../lib/cloudfront-import-stack");
const app = new cdk.App();
// Environment configuration for your AWS account
const env = {
    account: '************',
    region: 'us-east-1',
};
// CloudFront import stack - imports existing Terraform-managed distributions
new cloudfront_import_stack_1.CloudFrontImportStack(app, 'CloudFrontImportStack', {
    env: env,
    description: 'Import existing CloudFront distributions from Terraform for migration to CDK',
    tags: {
        Project: 'Terraform to CDK Migration',
        Environment: 'Development',
        ManagedBy: 'CDK',
        Purpose: 'Import-Existing-Resources',
    },
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXdzbWNwZGVtby5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImF3c21jcGRlbW8udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxpREFBbUM7QUFDbkMsNEVBQXVFO0FBRXZFLE1BQU0sR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDO0FBRTFCLGlEQUFpRDtBQUNqRCxNQUFNLEdBQUcsR0FBRztJQUNWLE9BQU8sRUFBRSxjQUFjO0lBQ3ZCLE1BQU0sRUFBRSxXQUFXO0NBQ3BCLENBQUM7QUFFRiw2RUFBNkU7QUFDN0UsSUFBSSwrQ0FBcUIsQ0FBQyxHQUFHLEVBQUUsdUJBQXVCLEVBQUU7SUFDdEQsR0FBRyxFQUFFLEdBQUc7SUFDUixXQUFXLEVBQUUsOEVBQThFO0lBQzNGLElBQUksRUFBRTtRQUNKLE9BQU8sRUFBRSw0QkFBNEI7UUFDckMsV0FBVyxFQUFFLGFBQWE7UUFDMUIsU0FBUyxFQUFFLEtBQUs7UUFDaEIsT0FBTyxFQUFFLDJCQUEyQjtLQUNyQztDQUNGLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIiMhL3Vzci9iaW4vZW52IG5vZGVcbmltcG9ydCAqIGFzIGNkayBmcm9tICdhd3MtY2RrLWxpYic7XG5pbXBvcnQgeyBDbG91ZEZyb250SW1wb3J0U3RhY2sgfSBmcm9tICcuLi9saWIvY2xvdWRmcm9udC1pbXBvcnQtc3RhY2snO1xuXG5jb25zdCBhcHAgPSBuZXcgY2RrLkFwcCgpO1xuXG4vLyBFbnZpcm9ubWVudCBjb25maWd1cmF0aW9uIGZvciB5b3VyIEFXUyBhY2NvdW50XG5jb25zdCBlbnYgPSB7XG4gIGFjY291bnQ6ICc5MDI1Mzc2NTk0NTYnLFxuICByZWdpb246ICd1cy1lYXN0LTEnLFxufTtcblxuLy8gQ2xvdWRGcm9udCBpbXBvcnQgc3RhY2sgLSBpbXBvcnRzIGV4aXN0aW5nIFRlcnJhZm9ybS1tYW5hZ2VkIGRpc3RyaWJ1dGlvbnNcbm5ldyBDbG91ZEZyb250SW1wb3J0U3RhY2soYXBwLCAnQ2xvdWRGcm9udEltcG9ydFN0YWNrJywge1xuICBlbnY6IGVudixcbiAgZGVzY3JpcHRpb246ICdJbXBvcnQgZXhpc3RpbmcgQ2xvdWRGcm9udCBkaXN0cmlidXRpb25zIGZyb20gVGVycmFmb3JtIGZvciBtaWdyYXRpb24gdG8gQ0RLJyxcbiAgdGFnczoge1xuICAgIFByb2plY3Q6ICdUZXJyYWZvcm0gdG8gQ0RLIE1pZ3JhdGlvbicsXG4gICAgRW52aXJvbm1lbnQ6ICdEZXZlbG9wbWVudCcsXG4gICAgTWFuYWdlZEJ5OiAnQ0RLJyxcbiAgICBQdXJwb3NlOiAnSW1wb3J0LUV4aXN0aW5nLVJlc291cmNlcycsXG4gIH0sXG59KTsiXX0=