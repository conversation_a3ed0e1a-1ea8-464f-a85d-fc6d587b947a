import { legacy } from './index';
export type { BootstrapEnvironmentOptions, BootstrapSource } from './api/bootstrap';
export type { DeployStackResult } from './api/deployments';
export type * from './legacy-types';
export type { Account, ContextProviderPlugin } from './legacy-aws-auth';
export type { LoggerFunction } from './legacy-logging-source';
export declare const deepClone: typeof legacy.deepClone, flatten: typeof legacy.flatten, ifDefined: typeof legacy.ifDefined, isArray: (arg: any) => arg is any[], isEmpty: typeof legacy.isEmpty, numberFromBool: typeof legacy.numberFromBool, partition: typeof legacy.partition, deployStack: typeof legacy.deployStack, cli: typeof legacy.cli, exec: typeof legacy.exec, SdkProvider: typeof legacy.SdkProvider, PluginHost: typeof legacy.PluginHost, contentHash: typeof legacy.contentHash, Command: typeof legacy.Command, Configuration: typeof legacy.Configuration, PROJECT_CONTEXT: string, Settings: typeof legacy.Settings, Bootstrapper: typeof legacy.Bootstrapper, CloudExecutable: typeof legacy.CloudExecutable, execProgram: typeof legacy.execProgram, RequireApproval: typeof legacy.RequireApproval, leftPad: typeof legacy.leftPad, formatAsBanner: typeof legacy.formatAsBanner, enableTracing: typeof legacy.enableTracing, aliases: string[], command: string, describe: string, lowerCaseFirstCharacter: typeof legacy.lowerCaseFirstCharacter, deepMerge: typeof legacy.deepMerge, Deployments: typeof legacy.Deployments, rootDir: typeof legacy.rootDir, versionNumber: typeof legacy.versionNumber, availableInitTemplates: typeof legacy.availableInitTemplates, cached: typeof legacy.cached, CfnEvaluationException: typeof legacy.CfnEvaluationException, withCorkedLogging: typeof legacy.withCorkedLogging, LogLevel: typeof legacy.LogLevel, logLevel: legacy.LogLevel, CI: boolean, setLogLevel: typeof legacy.setLogLevel, setCI: typeof legacy.setCI, increaseVerbosity: typeof legacy.increaseVerbosity, trace: (fmt: string, ...args: unknown[]) => false | void, debug: (fmt: string, ...args: unknown[]) => false | void, error: (fmt: string, ...args: unknown[]) => void, warning: (fmt: string, ...args: unknown[]) => void, success: (fmt: string, ...args: unknown[]) => void, highlight: (fmt: string, ...args: unknown[]) => void, print: (fmt: string, ...args: unknown[]) => void, data: (fmt: string, ...args: unknown[]) => void, prefix: typeof legacy.prefix;
