import type * as cxapi from '@aws-cdk/cx-api';
import { BaseStackAssembly, StackCollection } from '../api/cloud-assembly';
export declare enum DefaultSelection {
    /**
     * Returns an empty selection in case there are no selectors.
     */
    None = "none",
    /**
     * If the app includes a single stack, returns it. Otherwise throws an exception.
     * This behavior is used by "deploy".
     */
    OnlySingle = "single",
    /**
     * Returns all stacks in the main (top level) assembly only.
     */
    MainAssembly = "main",
    /**
     * If no selectors are provided, returns all stacks in the app,
     * including stacks inside nested assemblies.
     */
    AllStacks = "all"
}
export interface SelectStacksOptions {
    /**
     * Extend the selection to upstread/downstream stacks
     * @default ExtendedStackSelection.None only select the specified stacks.
     */
    extend?: ExtendedStackSelection;
    /**
     * The behavior if no selectors are provided.
     */
    defaultBehavior: DefaultSelection;
    /**
     * Whether to deploy if the app contains no stacks.
     *
     * @default false
     */
    ignoreNoStacks?: boolean;
}
/**
 * When selecting stacks, what other stacks to include because of dependencies
 */
export declare enum ExtendedStackSelection {
    /**
     * Don't select any extra stacks
     */
    None = 0,
    /**
     * Include stacks that this stack depends on
     */
    Upstream = 1,
    /**
     * Include stacks that depend on this stack
     */
    Downstream = 2
}
/**
 * A specification of which stacks should be selected
 */
export interface StackSelector {
    /**
     * Whether all stacks at the top level assembly should
     * be selected and nothing else
     */
    allTopLevel?: boolean;
    /**
     * A list of patterns to match the stack hierarchical ids
     */
    patterns: string[];
}
/**
 * A single Cloud Assembly and the operations we do on it to deploy the artifacts inside
 */
export declare class CloudAssembly extends BaseStackAssembly {
    selectStacks(selector: StackSelector, options: SelectStacksOptions): Promise<StackCollection>;
    private selectTopLevelStacks;
    protected selectMatchingStacks(stacks: cxapi.CloudFormationStackArtifact[], patterns: string[], extend?: ExtendedStackSelection): Promise<StackCollection>;
    private selectDefaultStacks;
}
