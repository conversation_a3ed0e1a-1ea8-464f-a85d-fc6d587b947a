"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiGatewayDistributionStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront = __importStar(require("aws-cdk-lib/aws-cloudfront"));
const origins = __importStar(require("aws-cdk-lib/aws-cloudfront-origins"));
class ApiGatewayDistributionStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Distribution E1Y2GZXHXCGI8H - API Gateway with custom headers forwarding
        const apiGatewayDistribution1 = new cloudfront.Distribution(this, 'ApiGatewayDistribution1', {
            comment: 'API Gateway Distribution - E1Y2GZXHXCGI8H Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP1_1,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: false,
            defaultBehavior: {
                origin: new origins.HttpOrigin('p0z7suimt8.execute-api.us-east-1.amazonaws.com', {
                    originPath: '/dev',
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: false,
                cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache1', {
                    cachePolicyName: 'ApiGatewayCustomCachePolicy1',
                    comment: 'Custom cache policy for API Gateway distribution 1',
                    defaultTtl: cdk.Duration.seconds(0),
                    maxTtl: cdk.Duration.seconds(0),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.allowList('*'),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.allowList('Accept', 'Origin', 'Referrer'),
                }),
            },
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Distribution E131RZ69QYLX7O - API Gateway with cache behaviors
        const apiGatewayDistribution2 = new cloudfront.Distribution(this, 'ApiGatewayDistribution2', {
            comment: 'API Gateway Distribution with Cache Behaviors - E131RZ69QYLX7O Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP1_1,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: false,
            defaultBehavior: {
                origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
                    originPath: '/prod/regionTest',
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: false,
                cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache2', {
                    cachePolicyName: 'ApiGatewayCustomCachePolicy2',
                    comment: 'Custom cache policy for API Gateway distribution 2',
                    defaultTtl: cdk.Duration.seconds(0),
                    maxTtl: cdk.Duration.seconds(0),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.allowList('CloudFront-Is-Tablet-Viewer', 'CloudFront-Is-Mobile-Viewer', 'CloudFront-Is-SmartTV-Viewer', 'CloudFront-Is-Desktop-Viewer'),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.none(),
                }),
            },
            additionalBehaviors: {
                'Dir*/*.jpg': {
                    origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
                        originPath: '/prod/regionTest',
                        protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    }),
                    viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
                    allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
                    cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                    compress: false,
                    cachePolicy: new cloudfront.CachePolicy(this, 'JpgCachePolicy', {
                        cachePolicyName: 'JpgFilesCachePolicy',
                        comment: 'Cache policy for JPG files',
                        defaultTtl: cdk.Duration.seconds(86400),
                        maxTtl: cdk.Duration.seconds(31536000),
                        minTtl: cdk.Duration.seconds(0),
                        headerBehavior: cloudfront.CacheHeaderBehavior.none(),
                        queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
                        cookieBehavior: cloudfront.CacheCookieBehavior.none(),
                    }),
                },
            },
            errorResponses: [
                {
                    httpStatus: 403,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 404,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 502,
                    ttl: cdk.Duration.seconds(0),
                },
                {
                    httpStatus: 504,
                    ttl: cdk.Duration.seconds(0),
                },
            ],
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Distribution E2EZONTUD8DA1O - API Gateway with device detection headers
        const apiGatewayDistribution3 = new cloudfront.Distribution(this, 'ApiGatewayDistribution3', {
            comment: 'API Gateway Distribution with Device Detection - E2EZONTUD8DA1O Recreation',
            enabled: true,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            enableIpv6: true,
            defaultBehavior: {
                origin: new origins.HttpOrigin('9uerlzte0e.execute-api.us-east-1.amazonaws.com', {
                    originPath: '/test/testedge',
                    protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
                    httpsPort: 443,
                    httpPort: 80,
                    readTimeout: cdk.Duration.seconds(30),
                    keepaliveTimeout: cdk.Duration.seconds(5),
                    connectionAttempts: 3,
                    connectionTimeout: cdk.Duration.seconds(10),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
                cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
                compress: false,
                cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache3', {
                    cachePolicyName: 'ApiGatewayCustomCachePolicy3',
                    comment: 'Custom cache policy for API Gateway distribution 3',
                    defaultTtl: cdk.Duration.seconds(86400),
                    maxTtl: cdk.Duration.seconds(31536000),
                    minTtl: cdk.Duration.seconds(0),
                    headerBehavior: cloudfront.CacheHeaderBehavior.allowList('CloudFront-Viewer-Country', 'CloudFront-Forwarded-Proto', 'CloudFront-Is-Tablet-Viewer', 'CloudFront-Is-Mobile-Viewer', 'User-Agent', 'CloudFront-Is-SmartTV-Viewer', 'CloudFront-Is-Desktop-Viewer'),
                    queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
                    cookieBehavior: cloudfront.CacheCookieBehavior.none(),
                }),
            },
            certificate: undefined, // Uses CloudFront default certificate
            domainNames: [], // No custom domain names
            // No geo restrictions (equivalent to "none" in the original distribution)
        });
        // Output the distribution domain names
        new cdk.CfnOutput(this, 'ApiGatewayDistribution1DomainName', {
            value: apiGatewayDistribution1.distributionDomainName,
            description: 'Domain name for API Gateway Distribution 1',
        });
        new cdk.CfnOutput(this, 'ApiGatewayDistribution2DomainName', {
            value: apiGatewayDistribution2.distributionDomainName,
            description: 'Domain name for API Gateway Distribution 2',
        });
        new cdk.CfnOutput(this, 'ApiGatewayDistribution3DomainName', {
            value: apiGatewayDistribution3.distributionDomainName,
            description: 'Domain name for API Gateway Distribution 3',
        });
    }
}
exports.ApiGatewayDistributionStack = ApiGatewayDistributionStack;
//# sourceMappingURL=data:application/json;base64,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