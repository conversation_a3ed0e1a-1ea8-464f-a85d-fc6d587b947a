"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
/* eslint-disable import/no-relative-packages */
__exportStar(require("../../../@aws-cdk/toolkit-lib/lib/api/aws-auth"), exports);
__exportStar(require("../../../@aws-cdk/toolkit-lib/lib/api/aws-auth/private"), exports);
__exportStar(require("../../../@aws-cdk/toolkit-lib/lib/api/aws-auth/awscli-compatible"), exports);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXdzLWF1dGguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJhd3MtYXV0aC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsZ0RBQWdEO0FBQ2hELGlGQUErRDtBQUMvRCx5RkFBdUU7QUFDdkUsbUdBQWlGIiwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgaW1wb3J0L25vLXJlbGF0aXZlLXBhY2thZ2VzICovXG5leHBvcnQgKiBmcm9tICcuLi8uLi8uLi9AYXdzLWNkay90b29sa2l0LWxpYi9saWIvYXBpL2F3cy1hdXRoJztcbmV4cG9ydCAqIGZyb20gJy4uLy4uLy4uL0Bhd3MtY2RrL3Rvb2xraXQtbGliL2xpYi9hcGkvYXdzLWF1dGgvcHJpdmF0ZSc7XG5leHBvcnQgKiBmcm9tICcuLi8uLi8uLi9AYXdzLWNkay90b29sa2l0LWxpYi9saWIvYXBpL2F3cy1hdXRoL2F3c2NsaS1jb21wYXRpYmxlJztcblxuIl19