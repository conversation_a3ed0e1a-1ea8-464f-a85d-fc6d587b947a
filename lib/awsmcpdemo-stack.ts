import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as certificatemanager from 'aws-cdk-lib/aws-certificatemanager';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';

export class AwsmcpdemoStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create CloudFront distributions that recreate your existing ones
    this.createApiGatewayDistributions();
    this.createS3WebsiteDistributions();
    this.createComplexDistribution();
  }

  private createApiGatewayDistributions() {
    // Distribution E1Y2GZXHXCGI8H - API Gateway with custom headers forwarding
    const apiGatewayDist1 = new cloudfront.Distribution(this, 'ApiGatewayDist1', {
      comment: 'API Gateway Distribution - E1Y2GZXHXCGI8H Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP1_1,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: false,

      defaultBehavior: {
        origin: new origins.HttpOrigin('p0z7suimt8.execute-api.us-east-1.amazonaws.com', {
          originPath: '/dev',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          httpsPort: 443,
          httpPort: 80,
          readTimeout: cdk.Duration.seconds(30),
          keepaliveTimeout: cdk.Duration.seconds(5),
          connectionAttempts: 3,
          connectionTimeout: cdk.Duration.seconds(10),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache1', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy1',
          comment: 'Custom cache policy for API Gateway distribution 1',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList('*'),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.allowList('Accept', 'Origin', 'Referrer'),
        }),
      },
    });

    // Distribution E131RZ69QYLX7O - API Gateway with cache behaviors
    const apiGatewayDist2 = new cloudfront.Distribution(this, 'ApiGatewayDist2', {
      comment: 'API Gateway Distribution with Cache Behaviors - E131RZ69QYLX7O Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP1_1,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: false,

      defaultBehavior: {
        origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
          originPath: '/prod/regionTest',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache2', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy2',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'CloudFront-Is-Tablet-Viewer',
            'CloudFront-Is-Mobile-Viewer',
            'CloudFront-Is-SmartTV-Viewer',
            'CloudFront-Is-Desktop-Viewer'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },

      additionalBehaviors: {
        'Dir*/*.jpg': {
          origin: new origins.HttpOrigin('20rhz2v7tc.execute-api.us-west-2.amazonaws.com', {
            originPath: '/prod/regionTest',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'JpgCachePolicy', {
            cachePolicyName: 'JpgFilesCachePolicy',
            defaultTtl: cdk.Duration.seconds(86400),
            maxTtl: cdk.Duration.seconds(31536000),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
          }),
        },
      },

      errorResponses: [
        { httpStatus: 403, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 404, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 502, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 504, ttl: cdk.Duration.seconds(0) },
      ],
    });

    // Distribution E2EZONTUD8DA1O - API Gateway with device detection headers
    const apiGatewayDist3 = new cloudfront.Distribution(this, 'ApiGatewayDist3', {
      comment: 'API Gateway Distribution with Device Detection - E2EZONTUD8DA1O Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,

      defaultBehavior: {
        origin: new origins.HttpOrigin('9uerlzte0e.execute-api.us-east-1.amazonaws.com', {
          originPath: '/test/testedge',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ApiGatewayCache3', {
          cachePolicyName: 'ApiGatewayCustomCachePolicy3',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(31536000),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'CloudFront-Viewer-Country',
            'CloudFront-Forwarded-Proto',
            'CloudFront-Is-Tablet-Viewer',
            'CloudFront-Is-Mobile-Viewer',
            'User-Agent',
            'CloudFront-Is-SmartTV-Viewer',
            'CloudFront-Is-Desktop-Viewer'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },
    });

    // Outputs for API Gateway distributions
    new cdk.CfnOutput(this, 'ApiGatewayDist1DomainName', {
      value: apiGatewayDist1.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 1 (E1Y2GZXHXCGI8H)',
    });

    new cdk.CfnOutput(this, 'ApiGatewayDist2DomainName', {
      value: apiGatewayDist2.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 2 (E131RZ69QYLX7O)',
    });

    new cdk.CfnOutput(this, 'ApiGatewayDist3DomainName', {
      value: apiGatewayDist3.distributionDomainName,
      description: 'Domain name for API Gateway Distribution 3 (E2EZONTUD8DA1O)',
    });
  }

  private createS3WebsiteDistributions() {
    // Create a WAF Web ACL for S3 website distribution
    const webAcl = new wafv2.CfnWebACL(this, 'CloudFrontWebACL', {
      scope: 'CLOUDFRONT',
      defaultAction: { allow: {} },
      name: 'CreatedByCloudFront-MikesWebsite-WebACL',
      description: 'Web ACL for Mikes Website CloudFront Distribution',
      rules: [
        {
          name: 'AWSManagedRulesCommonRuleSet',
          priority: 1,
          overrideAction: { none: {} },
          statement: {
            managedRuleGroupStatement: {
              vendorName: 'AWS',
              name: 'AWSManagedRulesCommonRuleSet',
            },
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'CommonRuleSetMetric',
          },
        },
      ],
      visibilityConfig: {
        sampledRequestsEnabled: true,
        cloudWatchMetricsEnabled: true,
        metricName: 'MikesWebsiteWebACL',
      },
    });

    // Distribution E1YNWVHTAGY30T - S3 Website with WAF
    const s3WebsiteDist = new cloudfront.Distribution(this, 'S3WebsiteDist', {
      comment: 'S3 Website Distribution - E1YNWVHTAGY30T Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      webAclId: webAcl.attrArn,

      defaultBehavior: {
        origin: new origins.HttpOrigin('mikeswebsite.s3-website-us-east-1.amazonaws.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.fromCachePolicyId(
          this,
          'CachingOptimized',
          '658327ea-f89d-4fab-a63d-7e88639e58f6'
        ),
      },
    });

    // Distribution EXZC66UKJAABK - External website distribution
    const externalWebsiteDist = new cloudfront.Distribution(this, 'ExternalWebsiteDist', {
      comment: 'External Website Distribution - EXZC66UKJAABK Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,

      defaultBehavior: {
        origin: new origins.HttpOrigin('quotewizard.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'ExternalWebsiteCache', {
          cachePolicyName: 'ExternalWebsiteCachePolicy',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(86400),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
      },
    });

    // Distribution E10NZFW9D6AMU2 - Multi-origin distribution
    const multiOriginDist = new cloudfront.Distribution(this, 'MultiOriginDist', {
      comment: 'Multi-Origin Distribution - E10NZFW9D6AMU2 Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,

      defaultBehavior: {
        origin: new origins.HttpOrigin('cbth.quotewizard.com', {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: false,
        cachePolicy: new cloudfront.CachePolicy(this, 'MultiOriginCache', {
          cachePolicyName: 'MultiOriginCachePolicy',
          defaultTtl: cdk.Duration.seconds(86400),
          maxTtl: cdk.Duration.seconds(31536000),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
          cookieBehavior: cloudfront.CacheCookieBehavior.all(),
        }),
      },
    });

    // Outputs for S3 and website distributions
    new cdk.CfnOutput(this, 'S3WebsiteDistDomainName', {
      value: s3WebsiteDist.distributionDomainName,
      description: 'Domain name for S3 Website Distribution (E1YNWVHTAGY30T)',
    });

    new cdk.CfnOutput(this, 'ExternalWebsiteDistDomainName', {
      value: externalWebsiteDist.distributionDomainName,
      description: 'Domain name for External Website Distribution (EXZC66UKJAABK)',
    });

    new cdk.CfnOutput(this, 'MultiOriginDistDomainName', {
      value: multiOriginDist.distributionDomainName,
      description: 'Domain name for Multi-Origin Distribution (E10NZFW9D6AMU2)',
    });

    new cdk.CfnOutput(this, 'WebACLArn', {
      value: webAcl.attrArn,
      description: 'ARN of the WAF Web ACL',
    });
  }

  private createComplexDistribution() {
    // Import the existing ACM certificate
    const certificate = certificatemanager.Certificate.fromCertificateArn(
      this,
      'DeltyTestCertificate',
      'arn:aws:acm:us-east-1:902537659456:certificate/1396c717-ff76-403a-9ab1-cf2185a83196'
    );

    // Import the existing S3 bucket
    const deleteOnlyDevBucket = s3.Bucket.fromBucketName(
      this,
      'DeleteOnlyDevBucket',
      'deleteonly-dev'
    );

    // Distribution ES27SJ7X3UX1M - Complex multi-origin distribution with custom domain
    const complexDist = new cloudfront.Distribution(this, 'ComplexDist', {
      comment: 'Complex Multi-Origin Distribution - ES27SJ7X3UX1M Recreation',
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
      enableIpv6: true,
      certificate: certificate,
      domainNames: ['test-publisher.delty.com'],

      // Default behavior - Publisher Portal
      defaultBehavior: {
        origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
          originPath: '/dev/portal',
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          readTimeout: cdk.Duration.seconds(60),
          keepaliveTimeout: cdk.Duration.seconds(5),
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: true,
        cachePolicy: new cloudfront.CachePolicy(this, 'PublisherPortalCache', {
          cachePolicyName: 'PublisherPortalCachePolicy',
          defaultTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(0),
          minTtl: cdk.Duration.seconds(0),
          headerBehavior: cloudfront.CacheHeaderBehavior.none(),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
          cookieBehavior: cloudfront.CacheCookieBehavior.all(),
        }),
      },

      additionalBehaviors: {
        // SFTP Handler behavior - S3 origin
        '/sftphandler/*': {
          origin: new origins.S3Origin(deleteOnlyDevBucket),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'SftpHandlerCache', {
            cachePolicyName: 'SftpHandlerCachePolicy',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },

        // Bulk Edit behavior - S3 custom origin
        '/bulkedit/*': {
          origin: new origins.HttpOrigin('s3.amazonaws.com', {
            originPath: '/deleteonly-dev',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,
          cachePolicy: new cloudfront.CachePolicy(this, 'BulkEditCache', {
            cachePolicyName: 'BulkEditCachePolicy',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },

        // Kube behavior - Publisher Kube API
        'kube*': {
          origin: new origins.HttpOrigin('dqhg2xtwi2.execute-api.us-east-1.amazonaws.com', {
            originPath: '/dev',
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
            readTimeout: cdk.Duration.seconds(60),
            keepaliveTimeout: cdk.Duration.seconds(60),
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false,
          cachePolicy: new cloudfront.CachePolicy(this, 'KubeCache', {
            cachePolicyName: 'KubeCachePolicy',
            defaultTtl: cdk.Duration.seconds(0),
            maxTtl: cdk.Duration.seconds(0),
            minTtl: cdk.Duration.seconds(0),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
              'Authorization',
              'Origin',
              'Accept-Encoding'
            ),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(),
            cookieBehavior: cloudfront.CacheCookieBehavior.all(),
          }),
        },
      },

      // Custom error responses
      errorResponses: [
        { httpStatus: 403, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 404, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 500, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 501, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 502, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 503, ttl: cdk.Duration.seconds(0) },
        { httpStatus: 504, ttl: cdk.Duration.seconds(0) },
      ],
    });

    // Outputs for complex distribution
    new cdk.CfnOutput(this, 'ComplexDistDomainName', {
      value: complexDist.distributionDomainName,
      description: 'CloudFront domain name for complex distribution (ES27SJ7X3UX1M)',
    });

    new cdk.CfnOutput(this, 'ComplexDistId', {
      value: complexDist.distributionId,
      description: 'CloudFront distribution ID for complex distribution',
    });

    new cdk.CfnOutput(this, 'CustomDomainName', {
      value: 'test-publisher.delty.com',
      description: 'Custom domain name for the distribution',
    });
  }
}
