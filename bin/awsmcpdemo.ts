#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { CloudFrontImportStack } from '../lib/cloudfront-import-stack';

const app = new cdk.App();

// Environment configuration for your AWS account
const env = {
  account: '************',
  region: 'us-east-1',
};

// CloudFront import stack - imports existing Terraform-managed distributions
new CloudFrontImportStack(app, 'CloudFrontImportStack', {
  env: env,
  description: 'Import existing CloudFront distributions from Terraform for migration to CDK',
  tags: {
    Project: 'Terraform to CDK Migration',
    Environment: 'Development',
    ManagedBy: 'CDK',
    Purpose: 'Import-Existing-Resources',
  },
});