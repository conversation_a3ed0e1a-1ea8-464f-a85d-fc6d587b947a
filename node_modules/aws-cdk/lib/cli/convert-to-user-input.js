"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertYargsToUserInput = convertYargsToUserInput;
exports.convertConfigToUserInput = convertConfigToUserInput;
// @ts-ignore TS6133
function convertYargsToUserInput(args) {
    const globalOptions = {
        app: args.app,
        build: args.build,
        context: args.context,
        plugin: args.plugin,
        trace: args.trace,
        strict: args.strict,
        lookups: args.lookups,
        ignoreErrors: args.ignoreErrors,
        json: args.json,
        verbose: args.verbose,
        debug: args.debug,
        profile: args.profile,
        proxy: args.proxy,
        caBundlePath: args.caBundlePath,
        ec2creds: args.ec2creds,
        versionReporting: args.versionReporting,
        pathMetadata: args.pathMetadata,
        assetMetadata: args.assetMetadata,
        roleArn: args.roleArn,
        staging: args.staging,
        output: args.output,
        notices: args.notices,
        noColor: args.noColor,
        ci: args.ci,
        unstable: args.unstable,
    };
    let commandOptions;
    switch (args._[0]) {
        case 'list':
        case 'ls':
            commandOptions = {
                long: args.long,
                showDependencies: args.showDependencies,
                STACKS: args.STACKS,
            };
            break;
        case 'synth':
        case 'synthesize':
            commandOptions = {
                exclusively: args.exclusively,
                validation: args.validation,
                quiet: args.quiet,
                STACKS: args.STACKS,
            };
            break;
        case 'bootstrap':
            commandOptions = {
                bootstrapBucketName: args.bootstrapBucketName,
                bootstrapKmsKeyId: args.bootstrapKmsKeyId,
                examplePermissionsBoundary: args.examplePermissionsBoundary,
                customPermissionsBoundary: args.customPermissionsBoundary,
                bootstrapCustomerKey: args.bootstrapCustomerKey,
                qualifier: args.qualifier,
                publicAccessBlockConfiguration: args.publicAccessBlockConfiguration,
                tags: args.tags,
                execute: args.execute,
                trust: args.trust,
                trustForLookup: args.trustForLookup,
                untrust: args.untrust,
                cloudformationExecutionPolicies: args.cloudformationExecutionPolicies,
                force: args.force,
                terminationProtection: args.terminationProtection,
                showTemplate: args.showTemplate,
                toolkitStackName: args.toolkitStackName,
                template: args.template,
                previousParameters: args.previousParameters,
                ENVIRONMENTS: args.ENVIRONMENTS,
            };
            break;
        case 'gc':
            commandOptions = {
                action: args.action,
                type: args.type,
                rollbackBufferDays: args.rollbackBufferDays,
                createdBufferDays: args.createdBufferDays,
                confirm: args.confirm,
                bootstrapStackName: args.bootstrapStackName,
                ENVIRONMENTS: args.ENVIRONMENTS,
            };
            break;
        case 'deploy':
            commandOptions = {
                all: args.all,
                buildExclude: args.buildExclude,
                exclusively: args.exclusively,
                requireApproval: args.requireApproval,
                notificationArns: args.notificationArns,
                tags: args.tags,
                execute: args.execute,
                changeSetName: args.changeSetName,
                method: args.method,
                importExistingResources: args.importExistingResources,
                force: args.force,
                parameters: args.parameters,
                outputsFile: args.outputsFile,
                previousParameters: args.previousParameters,
                toolkitStackName: args.toolkitStackName,
                progress: args.progress,
                rollback: args.rollback,
                hotswap: args.hotswap,
                hotswapFallback: args.hotswapFallback,
                hotswapEcsMinimumHealthyPercent: args.hotswapEcsMinimumHealthyPercent,
                hotswapEcsMaximumHealthyPercent: args.hotswapEcsMaximumHealthyPercent,
                hotswapEcsStabilizationTimeoutSeconds: args.hotswapEcsStabilizationTimeoutSeconds,
                watch: args.watch,
                logs: args.logs,
                concurrency: args.concurrency,
                assetParallelism: args.assetParallelism,
                assetPrebuild: args.assetPrebuild,
                ignoreNoStacks: args.ignoreNoStacks,
                STACKS: args.STACKS,
            };
            break;
        case 'rollback':
            commandOptions = {
                all: args.all,
                toolkitStackName: args.toolkitStackName,
                force: args.force,
                validateBootstrapVersion: args.validateBootstrapVersion,
                orphan: args.orphan,
                STACKS: args.STACKS,
            };
            break;
        case 'import':
            commandOptions = {
                execute: args.execute,
                changeSetName: args.changeSetName,
                toolkitStackName: args.toolkitStackName,
                rollback: args.rollback,
                force: args.force,
                recordResourceMapping: args.recordResourceMapping,
                resourceMapping: args.resourceMapping,
                STACK: args.STACK,
            };
            break;
        case 'watch':
            commandOptions = {
                buildExclude: args.buildExclude,
                exclusively: args.exclusively,
                changeSetName: args.changeSetName,
                force: args.force,
                toolkitStackName: args.toolkitStackName,
                progress: args.progress,
                rollback: args.rollback,
                hotswap: args.hotswap,
                hotswapFallback: args.hotswapFallback,
                hotswapEcsMinimumHealthyPercent: args.hotswapEcsMinimumHealthyPercent,
                hotswapEcsMaximumHealthyPercent: args.hotswapEcsMaximumHealthyPercent,
                hotswapEcsStabilizationTimeoutSeconds: args.hotswapEcsStabilizationTimeoutSeconds,
                logs: args.logs,
                concurrency: args.concurrency,
                STACKS: args.STACKS,
            };
            break;
        case 'destroy':
            commandOptions = {
                all: args.all,
                exclusively: args.exclusively,
                force: args.force,
                STACKS: args.STACKS,
            };
            break;
        case 'diff':
            commandOptions = {
                exclusively: args.exclusively,
                contextLines: args.contextLines,
                template: args.template,
                strict: args.strict,
                securityOnly: args.securityOnly,
                fail: args.fail,
                processed: args.processed,
                quiet: args.quiet,
                changeSet: args.changeSet,
                importExistingResources: args.importExistingResources,
                STACKS: args.STACKS,
            };
            break;
        case 'drift':
            commandOptions = {
                fail: args.fail,
                STACKS: args.STACKS,
            };
            break;
        case 'metadata':
            commandOptions = {
                STACK: args.STACK,
            };
            break;
        case 'acknowledge':
        case 'ack':
            commandOptions = {
                ID: args.ID,
            };
            break;
        case 'notices':
            commandOptions = {
                unacknowledged: args.unacknowledged,
            };
            break;
        case 'init':
            commandOptions = {
                language: args.language,
                list: args.list,
                generateOnly: args.generateOnly,
                libVersion: args.libVersion,
                TEMPLATE: args.TEMPLATE,
            };
            break;
        case 'migrate':
            commandOptions = {
                stackName: args.stackName,
                language: args.language,
                account: args.account,
                region: args.region,
                fromPath: args.fromPath,
                fromStack: args.fromStack,
                outputPath: args.outputPath,
                fromScan: args.fromScan,
                filter: args.filter,
                compress: args.compress,
            };
            break;
        case 'context':
            commandOptions = {
                reset: args.reset,
                force: args.force,
                clear: args.clear,
            };
            break;
        case 'docs':
        case 'doc':
            commandOptions = {
                browser: args.browser,
            };
            break;
        case 'doctor':
            commandOptions = {};
            break;
        case 'refactor':
            commandOptions = {
                dryRun: args.dryRun,
                excludeFile: args.excludeFile,
                mappingFile: args.mappingFile,
                revert: args.revert,
                STACKS: args.STACKS,
            };
            break;
    }
    const userInput = {
        command: args._[0],
        globalOptions,
        [args._[0]]: commandOptions,
    };
    return userInput;
}
// @ts-ignore TS6133
function convertConfigToUserInput(config) {
    const globalOptions = {
        app: config.app,
        build: config.build,
        context: config.context,
        plugin: config.plugin,
        trace: config.trace,
        strict: config.strict,
        lookups: config.lookups,
        ignoreErrors: config.ignoreErrors,
        json: config.json,
        verbose: config.verbose,
        debug: config.debug,
        profile: config.profile,
        proxy: config.proxy,
        caBundlePath: config.caBundlePath,
        ec2creds: config.ec2creds,
        versionReporting: config.versionReporting,
        pathMetadata: config.pathMetadata,
        assetMetadata: config.assetMetadata,
        roleArn: config.roleArn,
        staging: config.staging,
        output: config.output,
        notices: config.notices,
        noColor: config.noColor,
        ci: config.ci,
        unstable: config.unstable,
    };
    const listOptions = {
        long: config.list?.long,
        showDependencies: config.list?.showDependencies,
    };
    const synthOptions = {
        exclusively: config.synth?.exclusively,
        validation: config.synth?.validation,
        quiet: config.synth?.quiet,
    };
    const bootstrapOptions = {
        bootstrapBucketName: config.bootstrap?.bootstrapBucketName,
        bootstrapKmsKeyId: config.bootstrap?.bootstrapKmsKeyId,
        examplePermissionsBoundary: config.bootstrap?.examplePermissionsBoundary,
        customPermissionsBoundary: config.bootstrap?.customPermissionsBoundary,
        bootstrapCustomerKey: config.bootstrap?.bootstrapCustomerKey,
        qualifier: config.bootstrap?.qualifier,
        publicAccessBlockConfiguration: config.bootstrap?.publicAccessBlockConfiguration,
        tags: config.bootstrap?.tags,
        execute: config.bootstrap?.execute,
        trust: config.bootstrap?.trust,
        trustForLookup: config.bootstrap?.trustForLookup,
        untrust: config.bootstrap?.untrust,
        cloudformationExecutionPolicies: config.bootstrap?.cloudformationExecutionPolicies,
        force: config.bootstrap?.force,
        terminationProtection: config.bootstrap?.terminationProtection,
        showTemplate: config.bootstrap?.showTemplate,
        toolkitStackName: config.bootstrap?.toolkitStackName,
        template: config.bootstrap?.template,
        previousParameters: config.bootstrap?.previousParameters,
    };
    const gcOptions = {
        action: config.gc?.action,
        type: config.gc?.type,
        rollbackBufferDays: config.gc?.rollbackBufferDays,
        createdBufferDays: config.gc?.createdBufferDays,
        confirm: config.gc?.confirm,
        bootstrapStackName: config.gc?.bootstrapStackName,
    };
    const deployOptions = {
        all: config.deploy?.all,
        buildExclude: config.deploy?.buildExclude,
        exclusively: config.deploy?.exclusively,
        requireApproval: config.deploy?.requireApproval,
        notificationArns: config.deploy?.notificationArns,
        tags: config.deploy?.tags,
        execute: config.deploy?.execute,
        changeSetName: config.deploy?.changeSetName,
        method: config.deploy?.method,
        importExistingResources: config.deploy?.importExistingResources,
        force: config.deploy?.force,
        parameters: config.deploy?.parameters,
        outputsFile: config.deploy?.outputsFile,
        previousParameters: config.deploy?.previousParameters,
        toolkitStackName: config.deploy?.toolkitStackName,
        progress: config.deploy?.progress,
        rollback: config.deploy?.rollback,
        hotswap: config.deploy?.hotswap,
        hotswapFallback: config.deploy?.hotswapFallback,
        hotswapEcsMinimumHealthyPercent: config.deploy?.hotswapEcsMinimumHealthyPercent,
        hotswapEcsMaximumHealthyPercent: config.deploy?.hotswapEcsMaximumHealthyPercent,
        hotswapEcsStabilizationTimeoutSeconds: config.deploy?.hotswapEcsStabilizationTimeoutSeconds,
        watch: config.deploy?.watch,
        logs: config.deploy?.logs,
        concurrency: config.deploy?.concurrency,
        assetParallelism: config.deploy?.assetParallelism,
        assetPrebuild: config.deploy?.assetPrebuild,
        ignoreNoStacks: config.deploy?.ignoreNoStacks,
    };
    const rollbackOptions = {
        all: config.rollback?.all,
        toolkitStackName: config.rollback?.toolkitStackName,
        force: config.rollback?.force,
        validateBootstrapVersion: config.rollback?.validateBootstrapVersion,
        orphan: config.rollback?.orphan,
    };
    const importOptions = {
        execute: config.import?.execute,
        changeSetName: config.import?.changeSetName,
        toolkitStackName: config.import?.toolkitStackName,
        rollback: config.import?.rollback,
        force: config.import?.force,
        recordResourceMapping: config.import?.recordResourceMapping,
        resourceMapping: config.import?.resourceMapping,
    };
    const watchOptions = {
        buildExclude: config.watch?.buildExclude,
        exclusively: config.watch?.exclusively,
        changeSetName: config.watch?.changeSetName,
        force: config.watch?.force,
        toolkitStackName: config.watch?.toolkitStackName,
        progress: config.watch?.progress,
        rollback: config.watch?.rollback,
        hotswap: config.watch?.hotswap,
        hotswapFallback: config.watch?.hotswapFallback,
        hotswapEcsMinimumHealthyPercent: config.watch?.hotswapEcsMinimumHealthyPercent,
        hotswapEcsMaximumHealthyPercent: config.watch?.hotswapEcsMaximumHealthyPercent,
        hotswapEcsStabilizationTimeoutSeconds: config.watch?.hotswapEcsStabilizationTimeoutSeconds,
        logs: config.watch?.logs,
        concurrency: config.watch?.concurrency,
    };
    const destroyOptions = {
        all: config.destroy?.all,
        exclusively: config.destroy?.exclusively,
        force: config.destroy?.force,
    };
    const diffOptions = {
        exclusively: config.diff?.exclusively,
        contextLines: config.diff?.contextLines,
        template: config.diff?.template,
        strict: config.diff?.strict,
        securityOnly: config.diff?.securityOnly,
        fail: config.diff?.fail,
        processed: config.diff?.processed,
        quiet: config.diff?.quiet,
        changeSet: config.diff?.changeSet,
        importExistingResources: config.diff?.importExistingResources,
    };
    const driftOptions = {
        fail: config.drift?.fail,
    };
    const metadataOptions = {};
    const acknowledgeOptions = {};
    const noticesOptions = {
        unacknowledged: config.notices?.unacknowledged,
    };
    const initOptions = {
        language: config.init?.language,
        list: config.init?.list,
        generateOnly: config.init?.generateOnly,
        libVersion: config.init?.libVersion,
    };
    const migrateOptions = {
        stackName: config.migrate?.stackName,
        language: config.migrate?.language,
        account: config.migrate?.account,
        region: config.migrate?.region,
        fromPath: config.migrate?.fromPath,
        fromStack: config.migrate?.fromStack,
        outputPath: config.migrate?.outputPath,
        fromScan: config.migrate?.fromScan,
        filter: config.migrate?.filter,
        compress: config.migrate?.compress,
    };
    const contextOptions = {
        reset: config.context?.reset,
        force: config.context?.force,
        clear: config.context?.clear,
    };
    const docsOptions = {
        browser: config.docs?.browser,
    };
    const doctorOptions = {};
    const refactorOptions = {
        dryRun: config.refactor?.dryRun,
        excludeFile: config.refactor?.excludeFile,
        mappingFile: config.refactor?.mappingFile,
        revert: config.refactor?.revert,
    };
    const userInput = {
        globalOptions,
        list: listOptions,
        synth: synthOptions,
        bootstrap: bootstrapOptions,
        gc: gcOptions,
        deploy: deployOptions,
        rollback: rollbackOptions,
        import: importOptions,
        watch: watchOptions,
        destroy: destroyOptions,
        diff: diffOptions,
        drift: driftOptions,
        metadata: metadataOptions,
        acknowledge: acknowledgeOptions,
        notices: noticesOptions,
        init: initOptions,
        migrate: migrateOptions,
        context: contextOptions,
        docs: docsOptions,
        doctor: doctorOptions,
        refactor: refactorOptions,
    };
    return userInput;
}
//# sourceMappingURL=data:application/json;base64,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